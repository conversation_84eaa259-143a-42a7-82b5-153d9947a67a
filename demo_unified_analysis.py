#!/usr/bin/env python3
"""
Demo script for the unified analysis architecture.

This script demonstrates the improved analysis layer with unified interfaces,
analysis pipeline, and consistent architecture.
"""

import sys
import tempfile
import shutil
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from data.models import CodeVariant
from data.repository import RepositoryManager
from data.database import DatabaseManager
from analysis import AnalysisPipeline, AnalysisConfig, BaseAnalyzer
from analysis.quality import QualityEvaluator


def create_sample_code_files(repo_path: Path):
    """Create sample Python files for analysis"""
    
    # Create main module with different quality levels
    main_file = repo_path / "main.py"
    main_file.write_text('''
"""
Main module demonstrating different code quality levels.
"""

import math
from typing import List, Optional


def fibonacci_recursive(n: int) -> int:
    """
    Calculate fibonacci number recursively (inefficient).
    
    Args:
        n: Input number
        
    Returns:
        Fibonacci number
    """
    if n <= 1:
        return n
    return fibonacci_recursive(n - 1) + fibonacci_recursive(n - 2)


def fibonacci_optimized(n: int) -> int:
    """
    Calculate fibonacci number iteratively (optimized).
    
    This implementation is much more efficient than the recursive version
    and demonstrates good coding practices.
    
    Args:
        n: Input number (must be non-negative)
        
    Returns:
        Fibonacci number
        
    Raises:
        ValueError: If n is negative
    """
    if n < 0:
        raise ValueError("Fibonacci is not defined for negative numbers")
    
    if n <= 1:
        return n
    
    a, b = 0, 1
    for _ in range(2, n + 1):
        a, b = b, a + b
    return b


def calculate_circle_area(radius: float) -> float:
    """
    Calculate the area of a circle.
    
    Args:
        radius: Circle radius (must be positive)
        
    Returns:
        Circle area
        
    Raises:
        ValueError: If radius is not positive
    """
    if radius <= 0:
        raise ValueError("Radius must be positive")
    
    return math.pi * radius ** 2


def process_numbers(numbers: List[int]) -> List[int]:
    """
    Process a list of numbers with various operations.
    
    Args:
        numbers: List of integers to process
        
    Returns:
        Processed list
    """
    result = []
    for num in numbers:
        if num > 0:
            if num % 2 == 0:
                result.append(num * 2)
            else:
                result.append(num * 3)
        else:
            result.append(0)
    return result


# Poor quality function for comparison
def bad_function(a,b,c,d,e):
    if a>b:
        if c>d:
            if e>10:
                x=a+b+c+d+e
                y=x*2
                z=y+1
                return z
            else:
                return a+b
        else:
            return c+d
    else:
        return e


def main():
    """Main function demonstrating the code."""
    print("Fibonacci recursive(10):", fibonacci_recursive(10))
    print("Fibonacci optimized(10):", fibonacci_optimized(10))
    print("Circle area (r=5):", calculate_circle_area(5))
    
    numbers = [1, 2, 3, 4, 5, -1, 0]
    processed = process_numbers(numbers)
    print("Processed numbers:", processed)
    
    print("Bad function result:", bad_function(1, 2, 3, 4, 15))


if __name__ == "__main__":
    main()
''')


def demo_unified_analysis_pipeline():
    """Demonstrate the unified analysis pipeline"""
    print("=== Unified Analysis Pipeline Demo ===")
    
    # Create temporary repository
    temp_dir = tempfile.mkdtemp()
    repo_path = Path(temp_dir) / "demo_repo"
    db_path = Path(temp_dir) / "analysis.db"
    
    try:
        # Initialize repository and create sample files
        repo_manager = RepositoryManager(str(repo_path))
        repo_manager.init_repo(str(repo_path))
        
        create_sample_code_files(repo_path)
        
        # Add and commit files
        repo_manager.repo.index.add(["main.py"])
        repo_manager.repo.index.commit("Initial commit with sample code")
        
        # Initialize database manager
        db_manager = DatabaseManager(str(db_path))
        
        # Create analysis configuration
        config = AnalysisConfig()
        config.set_analyzer_config('quality', {
            'enable_performance_testing': False,
            'enable_pylint': True,
            'enable_pyflakes': True
        })
        
        # Initialize analysis pipeline
        pipeline = AnalysisPipeline(str(repo_path), db_manager, config)
        
        print(f"Pipeline initialized with repository: {repo_path}")
        
        # Test functions with different quality levels
        test_functions = [
            ("fibonacci_optimized", "High quality function"),
            ("fibonacci_recursive", "Medium quality function"),
            ("bad_function", "Low quality function")
        ]
        
        print("\nAnalyzing different quality functions:")
        print("-" * 60)
        
        results = []
        for func_name, description in test_functions:
            print(f"\n🔍 Analyzing: {func_name} ({description})")
            
            try:
                # Perform comprehensive analysis
                variant = pipeline.analyze_comprehensive(
                    file_path="main.py",
                    symbol=func_name,
                    enable_temporal=True,
                    enable_spatial=True,
                    enable_quality=True
                )
                
                results.append((func_name, variant))
                
                # Display key metrics
                metrics = variant.metrics
                print(f"  📊 Quality Metrics:")
                print(f"    Performance:     {metrics.get('performance', 0):.3f}")
                print(f"    Readability:     {metrics.get('readability', 0):.3f}")
                print(f"    Complexity:      {metrics.get('complexity', 0):.3f}")
                print(f"    Maintainability: {metrics.get('maintainability', 0):.3f}")
                
                print(f"  🎯 Comprehensive Scores:")
                print(f"    Quality Score:   {metrics.get('quality_score', 0):.3f}")
                print(f"    Evolution Score: {metrics.get('evolution_score', 0):.3f}")
                print(f"    Overall Score:   {metrics.get('comprehensive_score', 0):.3f}")
                
                # Show objective values for multi-objective optimization
                if variant.objective_values:
                    print(f"  🎲 Objective Values: {[f'{v:.3f}' for v in variant.objective_values]}")
                
                # Show analysis results summary
                exec_results = variant.execution_results
                spatial_success = exec_results.get('spatial_analysis', {}).get('success', False)
                quality_success = exec_results.get('quality_evaluation', {}).get('evaluation_success', False)
                temporal_success = exec_results.get('temporal_analysis', {}).get('success', False)
                
                print(f"  ✅ Analysis Status:")
                print(f"    Spatial:  {'✓' if spatial_success else '✗'}")
                print(f"    Quality:  {'✓' if quality_success else '✗'}")
                print(f"    Temporal: {'✓' if temporal_success else '✗'}")
                
            except Exception as e:
                print(f"  ❌ Analysis failed: {e}")
        
        # Compare results
        print(f"\n📈 Comparison Summary:")
        print("-" * 60)
        
        # Sort by comprehensive score
        results.sort(key=lambda x: x[1].metrics.get('comprehensive_score', 0), reverse=True)
        
        for i, (func_name, variant) in enumerate(results, 1):
            score = variant.metrics.get('comprehensive_score', 0)
            print(f"{i}. {func_name:20} - Score: {score:.3f}")
        
        # Get pipeline statistics
        print(f"\n📊 Pipeline Statistics:")
        stats = pipeline.get_pipeline_statistics()
        print(f"  Repository: {stats['repo_path']}")
        print(f"  Database: {'✓' if stats['has_database'] else '✗'}")
        print(f"  Analyzers: {len(stats['analyzers'])}")
        
        if 'database_stats' in stats:
            db_stats = stats['database_stats']
            print(f"  Stored variants: {db_stats.get('total_variants', 0)}")
        
        # Cleanup
        pipeline.cleanup()
        
        print("\nUnified analysis pipeline: ✓ PASSED")
        
    except Exception as e:
        print(f"Pipeline demo error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        shutil.rmtree(temp_dir, ignore_errors=True)
    
    print()


def demo_analysis_config():
    """Demonstrate analysis configuration management"""
    print("=== Analysis Configuration Demo ===")
    
    try:
        # Create default configuration
        config = AnalysisConfig()
        
        print("Default configuration:")
        for analyzer_type in ['temporal', 'spatial', 'quality']:
            analyzer_config = config.get_analyzer_config(analyzer_type)
            print(f"  {analyzer_type}: {len(analyzer_config)} settings")
        
        # Customize configuration
        config.set_analyzer_config('quality', {
            'enable_performance_testing': True,
            'timeout_seconds': 60
        })
        
        config.set_global_config({
            'log_level': 'DEBUG',
            'max_memory_mb': 2048
        })
        
        print("\nCustomized configuration:")
        quality_config = config.get_analyzer_config('quality')
        print(f"  Quality analyzer timeout: {quality_config.get('timeout_seconds')}s")
        print(f"  Performance testing: {quality_config.get('enable_performance_testing')}")
        print(f"  Log level: {quality_config.get('log_level')}")
        
        print("Configuration management: ✓ PASSED")
        
    except Exception as e:
        print(f"Configuration demo error: {e}")
    
    print()


def demo_base_analyzer_interface():
    """Demonstrate base analyzer interface"""
    print("=== Base Analyzer Interface Demo ===")
    
    try:
        # Test with quality evaluator (which could inherit from BaseAnalyzer)
        evaluator = QualityEvaluator()
        
        # Create test variant
        variant = CodeVariant(
            code='''
def well_documented_function(x: int, y: int) -> int:
    """
    Add two numbers together.
    
    Args:
        x: First number
        y: Second number
        
    Returns:
        Sum of x and y
    """
    return x + y
''',
            commit_hash="test123",
            branch="main",
            symbol="well_documented_function"
        )
        
        print("Testing analyzer interface:")
        print(f"  Input variant: {variant.symbol}")
        
        # Analyze variant
        result = evaluator.evaluate_variant(variant)
        
        print(f"  Analysis completed: {'✓' if result.metrics else '✗'}")
        print(f"  Metrics count: {len(result.metrics)}")
        print(f"  Has objective values: {'✓' if result.objective_values else '✗'}")
        
        # Show some key metrics
        if result.metrics:
            print(f"  Sample metrics:")
            for key, value in list(result.metrics.items())[:3]:
                if isinstance(value, (int, float)):
                    print(f"    {key}: {value:.3f}")
        
        print("Base analyzer interface: ✓ PASSED")
        
    except Exception as e:
        print(f"Base analyzer demo error: {e}")
    
    print()


def main():
    """Run all unified analysis demos"""
    print("🔧 Unified Analysis Architecture Demo")
    print("=" * 60)
    print()
    
    try:
        demo_analysis_config()
        demo_base_analyzer_interface()
        demo_unified_analysis_pipeline()
        
        print("🎉 All unified analysis demos completed successfully!")
        print()
        print("Improved architecture features:")
        print("✓ Unified analysis pipeline")
        print("✓ Consistent analyzer interfaces")
        print("✓ Centralized configuration management")
        print("✓ Comprehensive metric calculation")
        print("✓ Multi-objective optimization support")
        print("✓ Enhanced error handling and logging")
        
    except Exception as e:
        print(f"❌ Demo failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
