"""
Repository Manager

Handles Git repository operations with high cohesion and low coupling.
"""

import os
import logging
from pathlib import Path
from typing import List, Optional, Dict, Any
from git import Repo, GitCommandError
from git.exc import InvalidGitRepositoryError

from .models import CodeVariant


class RepositoryError(Exception):
    """Custom exception for repository operations"""
    pass


class RepositoryManager:
    """
    Manages Git repository operations for the evolution system.
    
    This class provides a clean interface for Git operations while maintaining
    minimal knowledge about other system components.
    """
    
    def __init__(self, repo_path: str):
        """
        Initialize repository manager.
        
        Args:
            repo_path: Path to the Git repository
        """
        self.repo_path = Path(repo_path)
        self._repo: Optional[Repo] = None
        self._logger = logging.getLogger(__name__)
    
    @property
    def repo(self) -> Repo:
        """Get or initialize Git repository"""
        if self._repo is None:
            try:
                self._repo = Repo(self.repo_path)
            except InvalidGitRepositoryError:
                raise RepositoryError(f"Invalid Git repository: {self.repo_path}")
        return self._repo
    
    def clone_repo(self, url: str, path: str) -> None:
        """
        Clone a repository from URL.
        
        Args:
            url: Repository URL
            path: Local path for cloning
            
        Raises:
            RepositoryError: If cloning fails
        """
        try:
            self._logger.info(f"Cloning repository from {url} to {path}")
            Repo.clone_from(url, path)
            self.repo_path = Path(path)
            self._repo = None  # Reset to reload
            self._logger.info("Repository cloned successfully")
        except GitCommandError as e:
            raise RepositoryError(f"Failed to clone repository: {e}")
    
    def init_repo(self, path: str) -> None:
        """
        Initialize a new Git repository.
        
        Args:
            path: Path for new repository
            
        Raises:
            RepositoryError: If initialization fails
        """
        try:
            self._logger.info(f"Initializing repository at {path}")
            os.makedirs(path, exist_ok=True)
            Repo.init(path)
            self.repo_path = Path(path)
            self._repo = None  # Reset to reload
            self._logger.info("Repository initialized successfully")
        except Exception as e:
            raise RepositoryError(f"Failed to initialize repository: {e}")
    
    def commit_variant(self, variant: CodeVariant, message: Optional[str] = None) -> str:
        """
        Commit a code variant to the repository.
        
        Args:
            variant: Code variant to commit
            message: Optional commit message
            
        Returns:
            Commit hash
            
        Raises:
            RepositoryError: If commit fails
        """
        try:
            # Write code to file if file_path is specified
            if variant.file_path:
                file_path = self.repo_path / variant.file_path
                file_path.parent.mkdir(parents=True, exist_ok=True)
                file_path.write_text(variant.code, encoding='utf-8')
                
                # Add file to git
                self.repo.index.add([str(file_path)])
            
            # Create commit message
            if message is None:
                message = f"Evolution variant for {variant.symbol or 'unknown'}"
                if variant.pareto_rank is not None:
                    message += f" (Pareto rank: {variant.pareto_rank})"
            
            # Commit changes
            commit = self.repo.index.commit(message)
            commit_hash = commit.hexsha
            
            # Update variant with commit hash
            variant.commit_hash = commit_hash
            
            self._logger.info(f"Committed variant: {commit_hash}")
            return commit_hash
            
        except GitCommandError as e:
            raise RepositoryError(f"Failed to commit variant: {e}")
    
    def create_branch(self, branch_name: str, checkout: bool = True) -> None:
        """
        Create a new branch.
        
        Args:
            branch_name: Name of the new branch
            checkout: Whether to checkout the new branch
            
        Raises:
            RepositoryError: If branch creation fails
        """
        try:
            self._logger.info(f"Creating branch: {branch_name}")
            
            # Create new branch
            new_branch = self.repo.create_head(branch_name)
            
            if checkout:
                new_branch.checkout()
                
            self._logger.info(f"Branch {branch_name} created successfully")
            
        except GitCommandError as e:
            raise RepositoryError(f"Failed to create branch {branch_name}: {e}")
    
    def checkout_branch(self, branch_name: str) -> None:
        """
        Checkout an existing branch.
        
        Args:
            branch_name: Name of the branch to checkout
            
        Raises:
            RepositoryError: If checkout fails
        """
        try:
            self._logger.info(f"Checking out branch: {branch_name}")
            self.repo.heads[branch_name].checkout()
            self._logger.info(f"Checked out branch: {branch_name}")
        except (GitCommandError, IndexError) as e:
            raise RepositoryError(f"Failed to checkout branch {branch_name}: {e}")
    
    def merge_branches(self, source_branch: str, target_branch: str) -> CodeVariant:
        """
        Merge source branch into target branch.
        
        Args:
            source_branch: Source branch name
            target_branch: Target branch name
            
        Returns:
            CodeVariant representing the merge result
            
        Raises:
            RepositoryError: If merge fails
        """
        try:
            self._logger.info(f"Merging {source_branch} into {target_branch}")
            
            # Checkout target branch
            self.checkout_branch(target_branch)
            
            # Perform merge
            source_commit = self.repo.heads[source_branch].commit
            merge_base = self.repo.merge_base(self.repo.head.commit, source_commit)[0]
            
            # Simple merge (no conflict resolution for now)
            self.repo.index.merge_tree(source_commit, base=merge_base)
            
            # Commit merge
            merge_commit = self.repo.index.commit(
                f"Merge branch '{source_branch}' into '{target_branch}'"
            )
            
            # Create variant for merge result
            variant = CodeVariant(
                code="",  # Will be populated by caller if needed
                commit_hash=merge_commit.hexsha,
                branch=target_branch,
                file_path=None,
                symbol=f"merge_{source_branch}_{target_branch}"
            )
            
            self._logger.info(f"Merged successfully: {merge_commit.hexsha}")
            return variant
            
        except GitCommandError as e:
            raise RepositoryError(f"Failed to merge branches: {e}")
    
    def get_current_branch(self) -> str:
        """Get current branch name"""
        try:
            return self.repo.active_branch.name
        except Exception:
            return "HEAD"  # Detached HEAD state
    
    def list_branches(self) -> List[str]:
        """List all branches"""
        try:
            return [head.name for head in self.repo.heads]
        except GitCommandError as e:
            raise RepositoryError(f"Failed to list branches: {e}")
    
    def get_commit_info(self, commit_hash: str) -> Dict[str, Any]:
        """
        Get commit information.
        
        Args:
            commit_hash: Commit hash
            
        Returns:
            Dictionary with commit information
        """
        try:
            commit = self.repo.commit(commit_hash)
            return {
                "hash": commit.hexsha,
                "message": commit.message.strip(),
                "author": str(commit.author),
                "timestamp": commit.committed_datetime.isoformat(),
                "files": list(commit.stats.files.keys())
            }
        except GitCommandError as e:
            raise RepositoryError(f"Failed to get commit info: {e}")
    
    def create_tag(self, tag_name: str, message: Optional[str] = None) -> None:
        """
        Create a Git tag.
        
        Args:
            tag_name: Tag name
            message: Optional tag message
        """
        try:
            self.repo.create_tag(tag_name, message=message)
            self._logger.info(f"Created tag: {tag_name}")
        except GitCommandError as e:
            raise RepositoryError(f"Failed to create tag {tag_name}: {e}")
    
    def get_file_content(self, file_path: str, commit_hash: Optional[str] = None) -> str:
        """
        Get file content from repository.
        
        Args:
            file_path: Path to file
            commit_hash: Optional commit hash (defaults to HEAD)
            
        Returns:
            File content as string
        """
        try:
            if commit_hash:
                commit = self.repo.commit(commit_hash)
                blob = commit.tree[file_path]
                return blob.data_stream.read().decode('utf-8')
            else:
                full_path = self.repo_path / file_path
                return full_path.read_text(encoding='utf-8')
        except Exception as e:
            raise RepositoryError(f"Failed to get file content: {e}")
