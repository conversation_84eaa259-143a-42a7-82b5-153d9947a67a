# 数据层 (Data Layer)

数据层是持续进化编程工具的核心基础设施，负责管理代码、索引、存档和 Pareto 解。按照高内聚低耦合的原则设计，提供清晰的接口和最小知识原则。

## 架构概述

数据层包含以下核心组件：

- **统一数据模型** (`models.py`) - 定义 CodeVariant 和相关数据结构
- **仓库管理** (`repository.py`) - Git 仓库操作
- **数据库管理** (`database.py`) - SQLite 数据存储
- **日志存储** (`storage.py`) - 结构化日志管理

## 核心组件

### 1. CodeVariant 数据模型

`CodeVariant` 是系统的核心数据结构，包含：

```python
class CodeVariant(BaseModel):
    # 核心代码信息
    code: str                           # 代码内容
    commit_hash: str                    # Git 提交哈希
    branch: str                         # Git 分支 (Island)
    timestamp: str                      # 创建时间戳
    
    # 质量和执行指标
    metrics: Dict[str, float]           # 质量指标
    execution_results: Dict[str, Any]   # 执行结果
    
    # MAP-Elites 网格位置
    grid_pos: Optional[Tuple[int, int]] # 网格坐标
    
    # 多目标优化字段
    pareto_rank: Optional[int]          # Pareto 排名
    objective_values: List[float]       # 多目标值
```

**特性：**
- 基于 Pydantic 的数据验证
- 支持序列化/反序列化
- 提供便捷的更新方法
- 支持多目标优化信息

### 2. RepositoryManager 仓库管理

负责所有 Git 操作，提供：

```python
class RepositoryManager:
    def clone_repo(url: str, path: str) -> None
    def init_repo(path: str) -> None
    def commit_variant(variant: CodeVariant) -> str
    def create_branch(branch_name: str) -> None
    def checkout_branch(branch_name: str) -> None
    def merge_branches(source: str, target: str) -> CodeVariant
    def get_commit_info(commit_hash: str) -> Dict[str, Any]
```

**特性：**
- 完整的 Git 操作支持
- 自动处理代码变体提交
- 分支管理和合并
- 错误处理和日志记录

### 3. DatabaseManager 数据库管理

管理 SQLite 数据库操作：

```python
class DatabaseManager:
    def store_variant(variant: CodeVariant) -> int
    def query_variants(branch: str = None, ...) -> List[CodeVariant]
    def store_pareto_archive(variant_id: int, ...) -> None
    def query_pareto_archive(generation: int = None) -> List[Dict]
    def log_evolution_event(event_type: str, ...) -> None
    def get_statistics() -> Dict[str, Any]
```

**数据库模式：**
- `variants` - 存储代码变体
- `pareto_archive` - Pareto 存档
- `evolution_log` - 演化事件日志

**特性：**
- 自动数据库初始化
- JSON 字段支持
- 索引优化
- 事务安全

### 4. LogStorage 日志存储

提供结构化日志管理：

```python
class LogStorage:
    def log_evolution_event(event_type: str, details: Dict) -> None
    def log_performance_metrics(operation: str, metrics: Dict) -> None
    def log_error(error_type: str, message: str, context: Dict) -> None
    def log_git_operation(operation: str, details: Dict) -> None
    def log_database_operation(operation: str, details: Dict) -> None
```

**特性：**
- 分类日志记录（演化、性能、错误、Git、数据库）
- JSON 格式化
- 文件轮转
- 日志检索和清理

## 使用示例

### 创建和操作代码变体

```python
from src.data import CodeVariant

# 创建变体
variant = CodeVariant(
    code="def fibonacci(n): return n if n <= 1 else fibonacci(n-1) + fibonacci(n-2)",
    commit_hash="abc123",
    branch="main",
    file_path="fibonacci.py",
    symbol="fibonacci"
)

# 更新指标
variant.update_metrics({
    "performance": 0.8,
    "readability": 0.9,
    "complexity": 0.6
})

# 设置 Pareto 信息
variant.set_pareto_info(rank=1, objectives=[0.8, 0.9, 0.6])
```

### 仓库操作

```python
from src.data import RepositoryManager

repo_manager = RepositoryManager("/path/to/repo")
repo_manager.init_repo("/path/to/repo")

# 提交变体
commit_hash = repo_manager.commit_variant(variant)

# 创建分支
repo_manager.create_branch("feature-branch")
```

### 数据库操作

```python
from src.data import DatabaseManager

db_manager = DatabaseManager("/path/to/database.db")

# 存储变体
variant_id = db_manager.store_variant(variant)

# 查询变体
variants = db_manager.query_variants(branch="main")

# 存储 Pareto 存档
db_manager.store_pareto_archive(variant_id, generation=1, rank=1, objectives=[0.8, 0.9])
```

### 日志记录

```python
from src.data import LogStorage

log_storage = LogStorage("/path/to/logs")

# 记录演化事件
log_storage.log_evolution_event("mutation_applied", {
    "variant_id": 123,
    "mutation_type": "function_rename"
})

# 记录性能指标
log_storage.log_performance_metrics("optimization", {
    "duration": 2.5,
    "memory_usage": 150
})
```

## 设计原则

### 高内聚低耦合
- 每个模块职责单一明确
- 模块间通过清晰接口交互
- 最小化模块间依赖

### 最小知识原则
- 每个组件只了解必要的信息
- 避免跨层级的直接访问
- 通过数据模型进行通信

### 错误处理
- 自定义异常类型
- 完整的错误上下文
- 优雅的错误恢复

### 可扩展性
- 支持新的质量指标
- 可配置的数据库模式
- 灵活的日志分类

## 依赖项

- `gitpython>=3.1.44` - Git 操作
- `pydantic>=2.11.6` - 数据模型验证
- `sqlite3` - 数据库（Python 内置）

## 测试

运行演示脚本验证功能：

```bash
uv run python demo_data_layer.py
```

## 下一步

数据层为后续模块提供了坚实的基础：

1. **分析层** - 将使用 CodeVariant 进行代码裁剪和质量评估
2. **处理层** - 将基于数据层进行变异、优化和演化
3. **控制层** - 将协调数据层组件完成工作流

数据层的设计确保了系统的可维护性、可扩展性和稳定性。
