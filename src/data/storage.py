"""
Log Storage Manager

Handles log storage and retrieval for the evolution system.
"""

import json
import logging
import logging.handlers
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional
from enum import Enum


class LogLevel(Enum):
    """Log levels for the evolution system"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class LogStorage:
    """
    Manages log storage for the evolution system.
    
    This class provides structured logging capabilities with JSON formatting
    and file rotation, maintaining minimal coupling with other components.
    """
    
    def __init__(self, log_dir: str, max_file_size: int = 10 * 1024 * 1024, backup_count: int = 5):
        """
        Initialize log storage.
        
        Args:
            log_dir: Directory for log files
            max_file_size: Maximum size per log file in bytes (default: 10MB)
            backup_count: Number of backup files to keep (default: 5)
        """
        self.log_dir = Path(log_dir)
        self.max_file_size = max_file_size
        self.backup_count = backup_count
        
        # Ensure log directory exists
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize loggers
        self._init_loggers()
    
    def _init_loggers(self) -> None:
        """Initialize different loggers for different purposes"""
        
        # Main evolution logger
        self.evolution_logger = self._create_logger(
            "evolution",
            self.log_dir / "evolution.log"
        )
        
        # Performance logger
        self.performance_logger = self._create_logger(
            "performance", 
            self.log_dir / "performance.log"
        )
        
        # Error logger
        self.error_logger = self._create_logger(
            "error",
            self.log_dir / "error.log"
        )
        
        # Git operations logger
        self.git_logger = self._create_logger(
            "git",
            self.log_dir / "git.log"
        )
        
        # Database operations logger
        self.database_logger = self._create_logger(
            "database",
            self.log_dir / "database.log"
        )
    
    def _create_logger(self, name: str, log_file: Path) -> logging.Logger:
        """Create a logger with rotating file handler"""
        
        logger = logging.getLogger(f"evo.{name}")
        logger.setLevel(logging.DEBUG)
        
        # Remove existing handlers to avoid duplicates
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)
        
        # Create rotating file handler
        handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=self.max_file_size,
            backupCount=self.backup_count,
            encoding='utf-8'
        )
        
        # Create JSON formatter
        formatter = JsonFormatter()
        handler.setFormatter(formatter)
        
        logger.addHandler(handler)
        logger.propagate = False  # Prevent duplicate logs
        
        return logger
    
    def log_evolution_event(self, event_type: str, details: Dict[str, Any], 
                           level: LogLevel = LogLevel.INFO) -> None:
        """
        Log evolution-related events.
        
        Args:
            event_type: Type of evolution event
            details: Event details
            level: Log level
        """
        log_data = {
            "event_type": event_type,
            "timestamp": datetime.now().isoformat(),
            **details
        }
        
        self._log_with_level(self.evolution_logger, level, "Evolution event", log_data)
    
    def log_performance_metrics(self, operation: str, metrics: Dict[str, Any]) -> None:
        """
        Log performance metrics.
        
        Args:
            operation: Operation name
            metrics: Performance metrics
        """
        log_data = {
            "operation": operation,
            "timestamp": datetime.now().isoformat(),
            "metrics": metrics
        }
        
        self.performance_logger.info("Performance metrics", extra={"data": log_data})
    
    def log_error(self, error_type: str, error_message: str, 
                  context: Optional[Dict[str, Any]] = None) -> None:
        """
        Log error events.
        
        Args:
            error_type: Type of error
            error_message: Error message
            context: Optional error context
        """
        log_data = {
            "error_type": error_type,
            "error_message": error_message,
            "timestamp": datetime.now().isoformat(),
            "context": context or {}
        }
        
        self.error_logger.error("Error occurred", extra={"data": log_data})
    
    def log_git_operation(self, operation: str, details: Dict[str, Any], 
                         success: bool = True) -> None:
        """
        Log Git operations.
        
        Args:
            operation: Git operation name
            details: Operation details
            success: Whether operation was successful
        """
        log_data = {
            "operation": operation,
            "success": success,
            "timestamp": datetime.now().isoformat(),
            **details
        }
        
        level = LogLevel.INFO if success else LogLevel.ERROR
        self._log_with_level(self.git_logger, level, "Git operation", log_data)
    
    def log_database_operation(self, operation: str, details: Dict[str, Any],
                              success: bool = True) -> None:
        """
        Log database operations.
        
        Args:
            operation: Database operation name
            details: Operation details
            success: Whether operation was successful
        """
        log_data = {
            "operation": operation,
            "success": success,
            "timestamp": datetime.now().isoformat(),
            **details
        }
        
        level = LogLevel.INFO if success else LogLevel.ERROR
        self._log_with_level(self.database_logger, level, "Database operation", log_data)
    
    def _log_with_level(self, logger: logging.Logger, level: LogLevel, 
                       message: str, data: Dict[str, Any]) -> None:
        """Log with specified level"""
        log_method = getattr(logger, level.value.lower())
        log_method(message, extra={"data": data})
    
    def get_logs(self, log_type: str, limit: int = 100) -> List[Dict[str, Any]]:
        """
        Retrieve logs from file.
        
        Args:
            log_type: Type of log (evolution, performance, error, git, database)
            limit: Maximum number of log entries to return
            
        Returns:
            List of log entries
        """
        log_file = self.log_dir / f"{log_type}.log"
        
        if not log_file.exists():
            return []
        
        logs = []
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
                # Get last 'limit' lines
                for line in lines[-limit:]:
                    try:
                        log_entry = json.loads(line.strip())
                        logs.append(log_entry)
                    except json.JSONDecodeError:
                        continue  # Skip malformed lines
                        
        except Exception as e:
            self.log_error("log_retrieval", f"Failed to retrieve logs: {e}")
        
        return logs
    
    def get_log_summary(self) -> Dict[str, Any]:
        """
        Get summary of all logs.
        
        Returns:
            Dictionary with log statistics
        """
        summary = {
            "log_files": {},
            "total_size": 0,
            "last_updated": None
        }
        
        for log_file in self.log_dir.glob("*.log"):
            if log_file.is_file():
                stat = log_file.stat()
                summary["log_files"][log_file.stem] = {
                    "size": stat.st_size,
                    "modified": datetime.fromtimestamp(stat.st_mtime).isoformat()
                }
                summary["total_size"] += stat.st_size
                
                # Update last modified time
                if (summary["last_updated"] is None or 
                    stat.st_mtime > datetime.fromisoformat(summary["last_updated"]).timestamp()):
                    summary["last_updated"] = datetime.fromtimestamp(stat.st_mtime).isoformat()
        
        return summary
    
    def cleanup_old_logs(self, days_to_keep: int = 30) -> None:
        """
        Clean up old log files.
        
        Args:
            days_to_keep: Number of days to keep logs
        """
        cutoff_time = datetime.now().timestamp() - (days_to_keep * 24 * 60 * 60)
        
        for log_file in self.log_dir.glob("*.log.*"):  # Backup files
            if log_file.stat().st_mtime < cutoff_time:
                try:
                    log_file.unlink()
                    self.evolution_logger.info(f"Cleaned up old log file: {log_file}")
                except Exception as e:
                    self.log_error("log_cleanup", f"Failed to delete {log_file}: {e}")


class JsonFormatter(logging.Formatter):
    """Custom JSON formatter for structured logging"""

    def format(self, record: logging.LogRecord) -> str:
        """Format log record as JSON"""

        log_entry = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno
        }

        # Add extra data if present
        if hasattr(record, 'data') and record.data:  # type: ignore
            log_entry.update(record.data)  # type: ignore

        # Add exception info if present
        if record.exc_info:
            log_entry["exception"] = self.formatException(record.exc_info)

        return json.dumps(log_entry, ensure_ascii=False)
