"""
Database Manager

Handles SQLite database operations for storing time indices and Pareto archives.
"""

import json
import sqlite3
import logging
from pathlib import Path
from typing import List, Optional, Dict, Any
from contextlib import contextmanager

from .models import CodeVariant, DatabaseSchema


class DatabaseError(Exception):
    """Custom exception for database operations"""
    pass


class DatabaseManager:
    """
    Manages SQLite database operations for the evolution system.
    
    This class provides a clean interface for database operations while maintaining
    minimal knowledge about other system components.
    """
    
    def __init__(self, db_path: str):
        """
        Initialize database manager.
        
        Args:
            db_path: Path to SQLite database file
        """
        self.db_path = Path(db_path)
        self._logger = logging.getLogger(__name__)
        self._init_database()
    
    def _init_database(self) -> None:
        """Initialize database with required tables"""
        try:
            # Ensure directory exists
            self.db_path.parent.mkdir(parents=True, exist_ok=True)
            
            with self._get_connection() as conn:
                # Create tables
                conn.execute(DatabaseSchema.VARIANTS_TABLE)
                conn.execute(DatabaseSchema.PARETO_ARCHIVE_TABLE)
                conn.execute(DatabaseSchema.EVOLUTION_LOG_TABLE)
                
                # Create indices for better performance
                conn.execute("""
                    CREATE INDEX IF NOT EXISTS idx_variants_branch_timestamp 
                    ON variants(branch, timestamp)
                """)
                conn.execute("""
                    CREATE INDEX IF NOT EXISTS idx_variants_commit_hash 
                    ON variants(commit_hash)
                """)
                conn.execute("""
                    CREATE INDEX IF NOT EXISTS idx_pareto_archive_generation 
                    ON pareto_archive(generation, rank)
                """)
                
                conn.commit()
                self._logger.info("Database initialized successfully")
                
        except sqlite3.Error as e:
            raise DatabaseError(f"Failed to initialize database: {e}")
    
    @contextmanager
    def _get_connection(self):
        """Get database connection with proper error handling"""
        conn = None
        try:
            conn = sqlite3.connect(str(self.db_path))
            conn.row_factory = sqlite3.Row  # Enable dict-like access
            yield conn
        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            raise DatabaseError(f"Database operation failed: {e}")
        finally:
            if conn:
                conn.close()
    
    def store_variant(self, variant: CodeVariant) -> int:
        """
        Store a code variant in the database.
        
        Args:
            variant: Code variant to store
            
        Returns:
            Database ID of stored variant
            
        Raises:
            DatabaseError: If storage fails
        """
        try:
            with self._get_connection() as conn:
                # Prepare data for insertion
                grid_x, grid_y = variant.grid_pos if variant.grid_pos else (None, None)
                
                cursor = conn.execute("""
                    INSERT OR REPLACE INTO variants (
                        commit_hash, branch, timestamp, code, file_path, symbol,
                        metrics, execution_results, grid_pos_x, grid_pos_y,
                        pareto_rank, objective_values
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    variant.commit_hash,
                    variant.branch,
                    variant.timestamp,
                    variant.code,
                    variant.file_path,
                    variant.symbol,
                    json.dumps(variant.metrics),
                    json.dumps(variant.execution_results),
                    grid_x,
                    grid_y,
                    variant.pareto_rank,
                    json.dumps(variant.objective_values)
                ))
                
                conn.commit()
                variant_id = cursor.lastrowid

                if variant_id is None:
                    raise DatabaseError("Failed to get variant ID after insertion")

                self._logger.info(f"Stored variant with ID: {variant_id}")
                return variant_id
                
        except sqlite3.Error as e:
            raise DatabaseError(f"Failed to store variant: {e}")
    
    def query_variants(
        self, 
        branch: Optional[str] = None,
        timestamp_from: Optional[str] = None,
        timestamp_to: Optional[str] = None,
        limit: Optional[int] = None
    ) -> List[CodeVariant]:
        """
        Query code variants from database.
        
        Args:
            branch: Optional branch filter
            timestamp_from: Optional start timestamp
            timestamp_to: Optional end timestamp
            limit: Optional result limit
            
        Returns:
            List of code variants
        """
        try:
            with self._get_connection() as conn:
                # Build query
                query = "SELECT * FROM variants WHERE 1=1"
                params = []
                
                if branch:
                    query += " AND branch = ?"
                    params.append(branch)
                
                if timestamp_from:
                    query += " AND timestamp >= ?"
                    params.append(timestamp_from)
                
                if timestamp_to:
                    query += " AND timestamp <= ?"
                    params.append(timestamp_to)
                
                query += " ORDER BY timestamp DESC"
                
                if limit:
                    query += " LIMIT ?"
                    params.append(limit)
                
                cursor = conn.execute(query, params)
                rows = cursor.fetchall()
                
                # Convert rows to CodeVariant objects
                variants = []
                for row in rows:
                    variant_data = dict(row)
                    
                    # Parse JSON fields
                    variant_data['metrics'] = json.loads(variant_data['metrics'] or '{}')
                    variant_data['execution_results'] = json.loads(variant_data['execution_results'] or '{}')
                    variant_data['objective_values'] = json.loads(variant_data['objective_values'] or '[]')
                    
                    # Handle grid position
                    if variant_data['grid_pos_x'] is not None and variant_data['grid_pos_y'] is not None:
                        variant_data['grid_pos'] = (variant_data['grid_pos_x'], variant_data['grid_pos_y'])
                    else:
                        variant_data['grid_pos'] = None
                    
                    # Remove database-specific fields
                    for key in ['id', 'grid_pos_x', 'grid_pos_y', 'created_at']:
                        variant_data.pop(key, None)
                    
                    variants.append(CodeVariant(**variant_data))
                
                self._logger.info(f"Queried {len(variants)} variants")
                return variants
                
        except sqlite3.Error as e:
            raise DatabaseError(f"Failed to query variants: {e}")
    
    def get_variant_by_commit(self, commit_hash: str) -> Optional[CodeVariant]:
        """
        Get variant by commit hash.
        
        Args:
            commit_hash: Git commit hash
            
        Returns:
            CodeVariant if found, None otherwise
        """
        variants = self.query_variants()
        for variant in variants:
            if variant.commit_hash == commit_hash:
                return variant
        return None
    
    def store_pareto_archive(self, variant_id: int, generation: int, rank: int, objectives: List[float]) -> None:
        """
        Store Pareto archive entry.
        
        Args:
            variant_id: Database ID of variant
            generation: Evolution generation
            rank: Pareto rank
            objectives: Objective values
        """
        try:
            with self._get_connection() as conn:
                conn.execute("""
                    INSERT INTO pareto_archive (variant_id, generation, rank, objectives)
                    VALUES (?, ?, ?, ?)
                """, (variant_id, generation, rank, json.dumps(objectives)))
                
                conn.commit()
                self._logger.info(f"Stored Pareto archive entry for variant {variant_id}")
                
        except sqlite3.Error as e:
            raise DatabaseError(f"Failed to store Pareto archive: {e}")
    
    def query_pareto_archive(self, generation: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Query Pareto archive.
        
        Args:
            generation: Optional generation filter
            
        Returns:
            List of Pareto archive entries
        """
        try:
            with self._get_connection() as conn:
                if generation is not None:
                    cursor = conn.execute("""
                        SELECT pa.*, v.commit_hash, v.branch 
                        FROM pareto_archive pa
                        JOIN variants v ON pa.variant_id = v.id
                        WHERE pa.generation = ?
                        ORDER BY pa.rank
                    """, (generation,))
                else:
                    cursor = conn.execute("""
                        SELECT pa.*, v.commit_hash, v.branch 
                        FROM pareto_archive pa
                        JOIN variants v ON pa.variant_id = v.id
                        ORDER BY pa.generation DESC, pa.rank
                    """)
                
                rows = cursor.fetchall()
                
                # Convert to list of dictionaries
                archive_entries = []
                for row in rows:
                    entry = dict(row)
                    entry['objectives'] = json.loads(entry['objectives'])
                    archive_entries.append(entry)
                
                return archive_entries
                
        except sqlite3.Error as e:
            raise DatabaseError(f"Failed to query Pareto archive: {e}")
    
    def log_evolution_event(self, event_type: str, branch: Optional[str] = None, 
                           generation: Optional[int] = None, details: Optional[Dict[str, Any]] = None) -> None:
        """
        Log evolution event.
        
        Args:
            event_type: Type of event
            branch: Optional branch name
            generation: Optional generation number
            details: Optional event details
        """
        try:
            with self._get_connection() as conn:
                conn.execute("""
                    INSERT INTO evolution_log (event_type, branch, generation, details)
                    VALUES (?, ?, ?, ?)
                """, (event_type, branch, generation, json.dumps(details or {})))
                
                conn.commit()
                self._logger.info(f"Logged evolution event: {event_type}")
                
        except sqlite3.Error as e:
            raise DatabaseError(f"Failed to log evolution event: {e}")
    
    def get_evolution_log(self, event_type: Optional[str] = None, 
                         branch: Optional[str] = None, limit: int = 100) -> List[Dict[str, Any]]:
        """
        Get evolution log entries.
        
        Args:
            event_type: Optional event type filter
            branch: Optional branch filter
            limit: Maximum number of entries
            
        Returns:
            List of log entries
        """
        try:
            with self._get_connection() as conn:
                query = "SELECT * FROM evolution_log WHERE 1=1"
                params = []
                
                if event_type:
                    query += " AND event_type = ?"
                    params.append(event_type)
                
                if branch:
                    query += " AND branch = ?"
                    params.append(branch)
                
                query += " ORDER BY timestamp DESC LIMIT ?"
                params.append(limit)
                
                cursor = conn.execute(query, params)
                rows = cursor.fetchall()
                
                # Convert to list of dictionaries
                log_entries = []
                for row in rows:
                    entry = dict(row)
                    entry['details'] = json.loads(entry['details'] or '{}')
                    log_entries.append(entry)
                
                return log_entries
                
        except sqlite3.Error as e:
            raise DatabaseError(f"Failed to get evolution log: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get database statistics"""
        try:
            with self._get_connection() as conn:
                stats = {}
                
                # Variant count by branch
                cursor = conn.execute("SELECT branch, COUNT(*) as count FROM variants GROUP BY branch")
                stats['variants_by_branch'] = dict(cursor.fetchall())
                
                # Total variants
                cursor = conn.execute("SELECT COUNT(*) FROM variants")
                stats['total_variants'] = cursor.fetchone()[0]
                
                # Pareto archive size
                cursor = conn.execute("SELECT COUNT(*) FROM pareto_archive")
                stats['pareto_archive_size'] = cursor.fetchone()[0]
                
                # Latest generation
                cursor = conn.execute("SELECT MAX(generation) FROM pareto_archive")
                latest_gen = cursor.fetchone()[0]
                stats['latest_generation'] = latest_gen if latest_gen is not None else 0
                
                return stats
                
        except sqlite3.Error as e:
            raise DatabaseError(f"Failed to get statistics: {e}")
