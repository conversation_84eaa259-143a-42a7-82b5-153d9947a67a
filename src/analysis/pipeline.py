"""
Analysis Pipeline

Provides unified analysis pipeline that coordinates multiple analyzers.
"""

import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from data.models import CodeVariant
from data.database import DatabaseManager
from .base import BaseAnalyzer, AnalysisConfig, AnalysisError
from .temporal import TemporalAnalyzer
from .spatial import SpatialAnalyzer
from .quality import QualityEvaluator


class AnalysisPipeline:
    """
    Unified analysis pipeline that coordinates multiple analyzers.
    
    This class provides a high-level interface for performing comprehensive
    code analysis using temporal, spatial, and quality analyzers.
    """
    
    def __init__(self, repo_path: str, db_manager: Optional[DatabaseManager] = None,
                 config: Optional[AnalysisConfig] = None):
        """
        Initialize analysis pipeline.
        
        Args:
            repo_path: Path to Git repository
            db_manager: Optional database manager for storing results
            config: Optional analysis configuration
        """
        self.repo_path = repo_path
        self.db_manager = db_manager
        self.config = config or AnalysisConfig()
        self._logger = logging.getLogger(__name__)
        
        # Initialize analyzers
        self._init_analyzers()
    
    def _init_analyzers(self) -> None:
        """Initialize all analyzers with appropriate configurations"""
        try:
            # Temporal analyzer
            temporal_config = self.config.get_analyzer_config('temporal')
            self.temporal_analyzer = TemporalAnalyzer(
                self.repo_path, 
                self.db_manager
            )
            
            # Spatial analyzer
            spatial_config = self.config.get_analyzer_config('spatial')
            self.spatial_analyzer = SpatialAnalyzer(self.repo_path)
            
            # Quality evaluator
            quality_config = self.config.get_analyzer_config('quality')
            self.quality_evaluator = QualityEvaluator(
                enable_performance_testing=quality_config.get('enable_performance_testing', False)
            )
            
            self._logger.info("Analysis pipeline initialized successfully")
            
        except Exception as e:
            raise AnalysisError(f"Failed to initialize analysis pipeline: {e}")
    
    def analyze_comprehensive(self, file_path: str, symbol: str, 
                            enable_temporal: bool = True,
                            enable_spatial: bool = True,
                            enable_quality: bool = True) -> CodeVariant:
        """
        Execute comprehensive analysis on a code symbol.
        
        Args:
            file_path: Path to file containing the symbol
            symbol: Symbol name to analyze
            enable_temporal: Whether to perform temporal analysis
            enable_spatial: Whether to perform spatial analysis
            enable_quality: Whether to perform quality analysis
            
        Returns:
            CodeVariant with comprehensive analysis results
            
        Raises:
            AnalysisError: If analysis fails
        """
        try:
            self._logger.info(f"Starting comprehensive analysis for {symbol} in {file_path}")
            
            # Step 1: Spatial analysis - extract related code
            if enable_spatial:
                self._logger.info("Performing spatial analysis...")
                variant = self.spatial_analyzer.extract_related_code(file_path, symbol)
            else:
                # Create basic variant if spatial analysis is disabled
                variant = CodeVariant(
                    code="",  # Will be populated if needed
                    commit_hash="",
                    branch="analysis",
                    file_path=file_path,
                    symbol=symbol
                )
            
            # Step 2: Quality evaluation - assess code quality
            if enable_quality:
                self._logger.info("Performing quality evaluation...")
                variant = self.quality_evaluator.evaluate_variant(variant)
            
            # Step 3: Temporal analysis - analyze evolution history
            if enable_temporal:
                self._logger.info("Performing temporal analysis...")
                variant = self._perform_temporal_analysis(variant)
            
            # Step 4: Calculate comprehensive metrics
            variant = self._calculate_comprehensive_metrics(variant)
            
            # Step 5: Store results if database manager is available
            if self.db_manager:
                self._store_analysis_results(variant)
            
            self._logger.info(f"Comprehensive analysis completed for {symbol}")
            return variant
            
        except Exception as e:
            self._logger.error(f"Comprehensive analysis failed: {e}")
            raise AnalysisError(f"Analysis pipeline failed: {e}")
    
    def _perform_temporal_analysis(self, variant: CodeVariant) -> CodeVariant:
        """
        Perform temporal analysis and integrate results into variant.
        
        Args:
            variant: Code variant to analyze
            
        Returns:
            Variant with temporal analysis results
        """
        try:
            file_path = variant.file_path
            symbol = variant.symbol
            
            if not file_path or not symbol:
                self._logger.warning("Missing file_path or symbol for temporal analysis")
                return variant
            
            # Build time index
            time_index = self.temporal_analyzer.build_time_index(file_path, symbol)
            
            # Analyze commit history
            diff_analyses = self.temporal_analyzer.analyze_commit_history(file_path)
            
            # Get related commits
            related_commits = self.temporal_analyzer.get_related_commits(file_path, symbol)
            
            # Update variant with temporal results
            variant.execution_results.update({
                'temporal_analysis': {
                    'time_index': time_index,
                    'diff_analyses_count': len(diff_analyses),
                    'related_commits_count': len(related_commits),
                    'analysis_timestamp': datetime.now().isoformat(),
                    'success': True
                }
            })
            
            # Add temporal metrics
            temporal_metrics = self._extract_temporal_metrics(time_index, diff_analyses)
            variant.update_metrics(temporal_metrics)
            
            return variant
            
        except Exception as e:
            self._logger.warning(f"Temporal analysis failed: {e}")
            # Add error information but don't fail the entire pipeline
            variant.execution_results['temporal_analysis'] = {
                'success': False,
                'error': str(e),
                'analysis_timestamp': datetime.now().isoformat()
            }
            return variant
    
    def _extract_temporal_metrics(self, time_index: Dict[str, Any], 
                                 diff_analyses: List[Any]) -> Dict[str, float]:
        """
        Extract temporal metrics from analysis results.
        
        Args:
            time_index: Time index data
            diff_analyses: List of diff analyses
            
        Returns:
            Dictionary of temporal metrics
        """
        metrics = {}
        
        # Change frequency metrics
        change_freq = time_index.get('change_frequency', {})
        metrics['change_frequency'] = change_freq.get('commits_per_day', 0.0)
        metrics['avg_change_score'] = change_freq.get('avg_change_score', 0.0)
        
        # Hotspot metrics
        hotspots = time_index.get('hotspots', [])
        metrics['hotspot_count'] = len(hotspots)
        metrics['hotspot_score'] = min(len(hotspots) / 10.0, 1.0)  # Normalize to 0-1
        
        # Evolution stability (lower change frequency = higher stability)
        if metrics['change_frequency'] > 0:
            metrics['evolution_stability'] = max(0.0, 1.0 - min(metrics['change_frequency'], 1.0))
        else:
            metrics['evolution_stability'] = 1.0
        
        # Commit activity
        metrics['total_commits'] = time_index.get('total_commits', 0)
        metrics['commit_activity'] = min(metrics['total_commits'] / 50.0, 1.0)  # Normalize
        
        return metrics
    
    def _calculate_comprehensive_metrics(self, variant: CodeVariant) -> CodeVariant:
        """
        Calculate comprehensive metrics combining all analysis results.
        
        Args:
            variant: Analyzed variant
            
        Returns:
            Variant with comprehensive metrics
        """
        metrics = variant.metrics
        
        # Quality metrics (from quality evaluator)
        performance = metrics.get('performance', 0.5)
        readability = metrics.get('readability', 0.5)
        complexity = metrics.get('complexity', 0.5)
        redundancy = metrics.get('redundancy', 0.5)
        maintainability = metrics.get('maintainability', 0.5)
        
        # Temporal metrics
        evolution_stability = metrics.get('evolution_stability', 0.5)
        hotspot_score = metrics.get('hotspot_score', 0.0)
        
        # Spatial metrics
        spatial_results = variant.execution_results.get('spatial_analysis', {})
        related_symbols_count = len(spatial_results.get('related_symbols', {}))
        spatial_complexity = min(related_symbols_count / 20.0, 1.0)  # Normalize
        
        # Calculate comprehensive scores
        
        # Quality score (higher is better)
        quality_score = (
            performance * 0.25 +
            readability * 0.25 +
            (1.0 - complexity) * 0.25 +  # Lower complexity is better
            (1.0 - redundancy) * 0.15 +  # Lower redundancy is better
            maintainability * 0.1
        )
        
        # Evolution score (higher is better)
        evolution_score = (
            evolution_stability * 0.7 +
            (1.0 - hotspot_score) * 0.3  # Lower hotspot score is better
        )
        
        # Complexity score (lower is better, so we invert it)
        complexity_score = 1.0 - (
            complexity * 0.4 +
            spatial_complexity * 0.3 +
            redundancy * 0.3
        )
        
        # Overall comprehensive score
        comprehensive_score = (
            quality_score * 0.5 +
            evolution_score * 0.3 +
            complexity_score * 0.2
        )
        
        # Update variant with comprehensive metrics
        variant.update_metrics({
            'quality_score': quality_score,
            'evolution_score': evolution_score,
            'complexity_score': complexity_score,
            'comprehensive_score': comprehensive_score,
            'spatial_complexity': spatial_complexity
        })
        
        # Update objective values for multi-objective optimization
        variant.objective_values = [
            quality_score,      # Quality objective
            evolution_score,    # Evolution objective
            complexity_score,   # Complexity objective (inverted)
            performance        # Performance objective
        ]
        
        return variant
    
    def _store_analysis_results(self, variant: CodeVariant) -> None:
        """
        Store analysis results in database.
        
        Args:
            variant: Analyzed variant to store
        """
        try:
            if self.db_manager:
                variant_id = self.db_manager.store_variant(variant)
                self._logger.info(f"Stored analysis results with ID: {variant_id}")
        except Exception as e:
            self._logger.warning(f"Failed to store analysis results: {e}")
    
    def analyze_batch(self, symbols: List[Dict[str, str]], 
                     **analysis_options) -> List[CodeVariant]:
        """
        Analyze multiple symbols in batch.
        
        Args:
            symbols: List of dictionaries with 'file_path' and 'symbol' keys
            **analysis_options: Options passed to analyze_comprehensive
            
        Returns:
            List of analyzed variants
        """
        results = []
        
        for symbol_info in symbols:
            try:
                file_path = symbol_info['file_path']
                symbol = symbol_info['symbol']
                
                variant = self.analyze_comprehensive(
                    file_path, symbol, **analysis_options
                )
                results.append(variant)
                
            except Exception as e:
                self._logger.error(f"Batch analysis failed for {symbol_info}: {e}")
                # Create error variant
                error_variant = CodeVariant(
                    code="",
                    commit_hash="error",
                    branch="error",
                    file_path=symbol_info.get('file_path', ''),
                    symbol=symbol_info.get('symbol', ''),
                    execution_results={'error': str(e)}
                )
                results.append(error_variant)
        
        return results
    
    def get_pipeline_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about the analysis pipeline.
        
        Returns:
            Dictionary with pipeline statistics
        """
        stats = {
            'repo_path': self.repo_path,
            'has_database': self.db_manager is not None,
            'analyzers': {
                'temporal': self.temporal_analyzer.__class__.__name__,
                'spatial': self.spatial_analyzer.__class__.__name__,
                'quality': self.quality_evaluator.__class__.__name__
            },
            'config': self.config.to_dict()
        }
        
        # Add database statistics if available
        if self.db_manager:
            try:
                db_stats = self.db_manager.get_statistics()
                stats['database_stats'] = db_stats
            except Exception as e:
                stats['database_error'] = str(e)
        
        return stats
    
    def cleanup(self) -> None:
        """Cleanup all analyzers and resources"""
        try:
            if hasattr(self.spatial_analyzer, 'cleanup'):
                self.spatial_analyzer.cleanup()
            self._logger.info("Analysis pipeline cleaned up successfully")
        except Exception as e:
            self._logger.warning(f"Cleanup warning: {e}")
