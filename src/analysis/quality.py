"""
Quality Evaluator

Handles multi-dimensional quality assessment using pylint, pyflakes, radon, and textstat.
"""

import ast
import time
import logging
import subprocess
import tempfile
from pathlib import Path
from typing import Dict, Any, List, Optional
from io import StringIO
import sys

try:
    import pylint.lint
    from pylint.reporters.text import TextReporter
except ImportError:
    pylint = None

try:
    import pyflakes.api
    import pyflakes.checker
except ImportError:
    pyflakes = None

try:
    import radon.complexity as radon_complexity
    import radon.metrics as radon_metrics
    from radon.raw import analyze
except ImportError:
    radon_complexity = None
    radon_metrics = None
    analyze = None

try:
    import textstat
except ImportError:
    textstat = None

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from data.models import CodeVariant


class QualityError(Exception):
    """Custom exception for quality evaluation operations"""
    pass


class QualityMetrics:
    """Container for quality metrics"""
    
    def __init__(self):
        self.performance: float = 0.0
        self.readability: float = 0.0
        self.complexity: float = 0.0
        self.redundancy: float = 0.0
        self.maintainability: float = 0.0
        self.style_score: float = 0.0
        self.error_count: int = 0
        self.warning_count: int = 0
        self.raw_metrics: Dict[str, Any] = {}


class QualityEvaluator:
    """
    Evaluates code quality using multiple static analysis tools.
    
    This class provides comprehensive quality assessment capabilities
    supporting multi-objective optimization requirements.
    """
    
    def __init__(self, enable_performance_testing: bool = False):
        """
        Initialize quality evaluator.
        
        Args:
            enable_performance_testing: Whether to enable actual performance testing
        """
        self.enable_performance_testing = enable_performance_testing
        self._logger = logging.getLogger(__name__)
    
    def evaluate_variant(self, variant: CodeVariant) -> CodeVariant:
        """
        Evaluate a code variant and update its metrics.
        
        Args:
            variant: Code variant to evaluate
            
        Returns:
            Updated variant with quality metrics
        """
        try:
            self._logger.info(f"Evaluating quality for variant: {variant.symbol}")
            
            # Calculate all quality metrics
            metrics = self._calculate_all_metrics(variant.code)
            
            # Update variant metrics
            variant.update_metrics({
                "performance": metrics.performance,
                "readability": metrics.readability,
                "complexity": metrics.complexity,
                "redundancy": metrics.redundancy,
                "maintainability": metrics.maintainability,
                "style_score": metrics.style_score,
                "error_count": metrics.error_count,
                "warning_count": metrics.warning_count
            })
            
            # Set objective values for multi-objective optimization
            variant.set_pareto_info(
                rank=None,  # Will be set by optimization algorithm
                objectives=[
                    metrics.performance,
                    metrics.readability,
                    1.0 - metrics.complexity,  # Lower complexity is better
                    1.0 - metrics.redundancy   # Lower redundancy is better
                ]
            )
            
            # Store detailed metrics in execution results
            variant.update_execution_results({
                "quality_evaluation": {
                    "timestamp": time.time(),
                    "raw_metrics": metrics.raw_metrics,
                    "tools_used": self._get_available_tools(),
                    "evaluation_success": True
                }
            })
            
            self._logger.info(f"Quality evaluation completed for {variant.symbol}")
            return variant
            
        except Exception as e:
            self._logger.error(f"Quality evaluation failed: {e}")
            
            # Set default metrics on failure
            variant.update_metrics({
                "performance": 0.0,
                "readability": 0.0,
                "complexity": 1.0,
                "redundancy": 1.0,
                "error_count": 1
            })
            
            variant.update_execution_results({
                "quality_evaluation": {
                    "timestamp": time.time(),
                    "error": str(e),
                    "evaluation_success": False
                }
            })
            
            return variant
    
    def _calculate_all_metrics(self, code: str) -> QualityMetrics:
        """Calculate all quality metrics for code"""
        metrics = QualityMetrics()
        
        # Performance metrics
        metrics.performance = self._calculate_performance_score(code)
        
        # Readability metrics
        metrics.readability = self._calculate_readability_score(code)
        
        # Complexity metrics
        metrics.complexity = self._calculate_complexity_score(code)
        
        # Redundancy metrics
        metrics.redundancy = self._calculate_redundancy_score(code)
        
        # Maintainability metrics
        metrics.maintainability = self._calculate_maintainability_score(code)
        
        # Style metrics
        style_results = self._calculate_style_score(code)
        metrics.style_score = style_results["score"]
        metrics.error_count = style_results["errors"]
        metrics.warning_count = style_results["warnings"]
        
        # Store raw metrics
        metrics.raw_metrics = {
            "performance_details": self._get_performance_details(code),
            "readability_details": self._get_readability_details(code),
            "complexity_details": self._get_complexity_details(code),
            "style_details": style_results
        }
        
        return metrics
    
    def _calculate_performance_score(self, code: str) -> float:
        """Calculate performance score (0.0 to 1.0, higher is better)"""
        try:
            if not self.enable_performance_testing:
                # Use static analysis for performance estimation
                return self._estimate_performance_static(code)
            
            # Actual performance testing (simplified)
            return self._measure_performance_dynamic(code)
            
        except Exception as e:
            self._logger.warning(f"Performance calculation failed: {e}")
            return 0.5  # Default neutral score
    
    def _estimate_performance_static(self, code: str) -> float:
        """Estimate performance using static analysis"""
        try:
            tree = ast.parse(code)
            score = 1.0
            
            # Penalize for nested loops
            for node in ast.walk(tree):
                if isinstance(node, ast.For):
                    # Check for nested loops
                    for child in ast.walk(node):
                        if isinstance(child, ast.For) and child != node:
                            score -= 0.1
                elif isinstance(node, ast.While):
                    score -= 0.05
                elif isinstance(node, ast.ListComp):
                    # List comprehensions are generally efficient
                    score += 0.02
            
            return max(0.0, min(1.0, score))
            
        except Exception:
            return 0.5
    
    def _measure_performance_dynamic(self, code: str) -> float:
        """Measure actual performance (simplified)"""
        try:
            # Create a simple test function
            test_code = f"""
import time
{code}

# Simple performance test
start_time = time.time()
# Execute main function if exists
try:
    if 'main' in locals():
        main()
    elif any(name.startswith('test_') for name in locals()):
        for name in locals():
            if name.startswith('test_'):
                locals()[name]()
                break
except:
    pass
end_time = time.time()
execution_time = end_time - start_time
"""
            
            # Execute in temporary environment
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                f.write(test_code)
                f.flush()
                
                try:
                    result = subprocess.run([
                        sys.executable, f.name
                    ], capture_output=True, timeout=5, text=True)
                    
                    # Simple scoring based on execution time
                    if result.returncode == 0:
                        return 0.8  # Successful execution
                    else:
                        return 0.3  # Failed execution
                        
                except subprocess.TimeoutExpired:
                    return 0.1  # Too slow
                finally:
                    Path(f.name).unlink(missing_ok=True)
                    
        except Exception:
            return 0.5
    
    def _calculate_readability_score(self, code: str) -> float:
        """Calculate readability score using textstat and custom metrics"""
        try:
            if textstat is None:
                return self._calculate_readability_simple(code)
            
            # Use textstat for readability analysis
            # Note: textstat is designed for natural language, so we adapt it
            
            # Extract comments and docstrings
            comments_and_docs = self._extract_comments_and_docstrings(code)
            
            if comments_and_docs:
                # Calculate readability of documentation
                flesch_score = textstat.flesch_reading_ease(comments_and_docs)
                # Convert to 0-1 scale (Flesch scores range from 0-100)
                doc_readability = flesch_score / 100.0
            else:
                doc_readability = 0.5  # Neutral if no documentation
            
            # Calculate code structure readability
            structure_score = self._calculate_structure_readability(code)
            
            # Combine scores
            return (doc_readability * 0.4 + structure_score * 0.6)
            
        except Exception as e:
            self._logger.warning(f"Readability calculation failed: {e}")
            return self._calculate_readability_simple(code)
    
    def _calculate_readability_simple(self, code: str) -> float:
        """Simple readability calculation without textstat"""
        try:
            lines = code.split('\n')
            non_empty_lines = [line for line in lines if line.strip()]
            
            if not non_empty_lines:
                return 0.0
            
            score = 1.0
            
            # Check average line length
            avg_line_length = sum(len(line) for line in non_empty_lines) / len(non_empty_lines)
            if avg_line_length > 100:
                score -= 0.2
            elif avg_line_length < 20:
                score -= 0.1
            
            # Check for comments
            comment_lines = [line for line in lines if line.strip().startswith('#')]
            comment_ratio = len(comment_lines) / len(non_empty_lines)
            score += comment_ratio * 0.3
            
            # Check for docstrings
            if '"""' in code or "'''" in code:
                score += 0.2
            
            return max(0.0, min(1.0, score))
            
        except Exception:
            return 0.5
    
    def _calculate_complexity_score(self, code: str) -> float:
        """Calculate complexity score using radon"""
        try:
            if radon_complexity is None:
                return self._calculate_complexity_simple(code)
            
            # Use radon for cyclomatic complexity
            complexity_results = radon_complexity.cc_visit(code)
            
            if not complexity_results:
                return 0.0
            
            # Calculate average complexity
            total_complexity = sum(result.complexity for result in complexity_results)
            avg_complexity = total_complexity / len(complexity_results)
            
            # Normalize to 0-1 scale (higher complexity = higher score, which is worse)
            # Typical complexity ranges: 1-10 (simple), 11-20 (moderate), 21+ (complex)
            normalized_complexity = min(avg_complexity / 20.0, 1.0)
            
            return normalized_complexity
            
        except Exception as e:
            self._logger.warning(f"Complexity calculation failed: {e}")
            return self._calculate_complexity_simple(code)
    
    def _calculate_complexity_simple(self, code: str) -> float:
        """Simple complexity calculation without radon"""
        try:
            tree = ast.parse(code)
            complexity = 0
            
            for node in ast.walk(tree):
                if isinstance(node, (ast.If, ast.For, ast.While, ast.Try)):
                    complexity += 1
                elif isinstance(node, ast.BoolOp):
                    complexity += len(node.values) - 1
            
            # Normalize to 0-1 scale
            return min(complexity / 10.0, 1.0)
            
        except Exception:
            return 0.5
    
    def _calculate_redundancy_score(self, code: str) -> float:
        """Calculate code redundancy score"""
        try:
            lines = [line.strip() for line in code.split('\n') if line.strip()]
            
            if len(lines) < 2:
                return 0.0
            
            # Count duplicate lines
            line_counts = {}
            for line in lines:
                line_counts[line] = line_counts.get(line, 0) + 1
            
            duplicate_lines = sum(count - 1 for count in line_counts.values() if count > 1)
            redundancy_ratio = duplicate_lines / len(lines)
            
            return min(redundancy_ratio, 1.0)
            
        except Exception:
            return 0.5
    
    def _calculate_maintainability_score(self, code: str) -> float:
        """Calculate maintainability score"""
        try:
            if radon_metrics is None:
                return self._calculate_maintainability_simple(code)
            
            # Use radon for maintainability index
            mi_results = radon_metrics.mi_visit(code, multi=True)
            
            if not mi_results:
                return 0.5
            
            # Average maintainability index
            avg_mi = sum(mi_results) / len(mi_results)
            
            # Maintainability index ranges from 0-100, normalize to 0-1
            return avg_mi / 100.0
            
        except Exception:
            return self._calculate_maintainability_simple(code)
    
    def _calculate_maintainability_simple(self, code: str) -> float:
        """Simple maintainability calculation"""
        try:
            # Combine readability, complexity, and structure
            readability = self._calculate_readability_simple(code)
            complexity = self._calculate_complexity_simple(code)
            
            # Higher readability and lower complexity = better maintainability
            maintainability = (readability + (1.0 - complexity)) / 2.0
            
            return maintainability
            
        except Exception:
            return 0.5
    
    def _calculate_style_score(self, code: str) -> Dict[str, Any]:
        """Calculate style score using pylint and pyflakes"""
        result = {
            "score": 0.5,
            "errors": 0,
            "warnings": 0,
            "details": []
        }
        
        try:
            # Use pyflakes for basic error checking
            if pyflakes is not None:
                pyflakes_result = self._run_pyflakes(code)
                result["errors"] += pyflakes_result["errors"]
                result["warnings"] += pyflakes_result["warnings"]
                result["details"].extend(pyflakes_result["details"])
            
            # Use pylint for comprehensive style checking
            if pylint is not None:
                pylint_result = self._run_pylint(code)
                result["score"] = pylint_result["score"]
                result["errors"] += pylint_result["errors"]
                result["warnings"] += pylint_result["warnings"]
                result["details"].extend(pylint_result["details"])
            
        except Exception as e:
            self._logger.warning(f"Style calculation failed: {e}")
        
        return result
    
    def _run_pyflakes(self, code: str) -> Dict[str, Any]:
        """Run pyflakes on code"""
        result = {"errors": 0, "warnings": 0, "details": []}
        
        try:
            # Capture pyflakes output
            old_stderr = sys.stderr
            sys.stderr = captured_output = StringIO()
            
            try:
                # Check code with pyflakes
                tree = ast.parse(code)
                checker = pyflakes.checker.Checker(tree, "<string>")
                
                # Count messages
                result["warnings"] = len(checker.messages)
                
                for message in checker.messages:
                    result["details"].append({
                        "tool": "pyflakes",
                        "type": "warning",
                        "message": str(message)
                    })
                    
            finally:
                sys.stderr = old_stderr
                
        except Exception as e:
            result["errors"] = 1
            result["details"].append({
                "tool": "pyflakes",
                "type": "error",
                "message": str(e)
            })
        
        return result
    
    def _run_pylint(self, code: str) -> Dict[str, Any]:
        """Run pylint on code"""
        result = {"score": 0.5, "errors": 0, "warnings": 0, "details": []}
        
        try:
            # Write code to temporary file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                f.write(code)
                f.flush()
                
                try:
                    # Capture pylint output
                    output = StringIO()
                    reporter = TextReporter(output)
                    
                    # Run pylint
                    pylint.lint.Run([f.name], reporter=reporter, exit=False)
                    
                    # Parse output (simplified)
                    output_text = output.getvalue()
                    
                    # Extract score (simplified parsing)
                    if "Your code has been rated at" in output_text:
                        score_line = [line for line in output_text.split('\n') 
                                    if "Your code has been rated at" in line][0]
                        score_str = score_line.split("rated at ")[1].split("/")[0]
                        result["score"] = float(score_str) / 10.0
                    
                    # Count errors and warnings (simplified)
                    result["warnings"] = output_text.count("warning")
                    result["errors"] = output_text.count("error")
                    
                except Exception as e:
                    result["details"].append({
                        "tool": "pylint",
                        "type": "error",
                        "message": str(e)
                    })
                finally:
                    Path(f.name).unlink(missing_ok=True)
                    
        except Exception as e:
            result["details"].append({
                "tool": "pylint",
                "type": "error", 
                "message": str(e)
            })
        
        return result
    
    def _extract_comments_and_docstrings(self, code: str) -> str:
        """Extract comments and docstrings from code"""
        try:
            tree = ast.parse(code)
            docs = []
            
            # Extract docstrings
            for node in ast.walk(tree):
                if isinstance(node, (ast.FunctionDef, ast.ClassDef, ast.Module)):
                    docstring = ast.get_docstring(node)
                    if docstring:
                        docs.append(docstring)
            
            # Extract comments
            lines = code.split('\n')
            comments = [line.strip()[1:].strip() for line in lines 
                       if line.strip().startswith('#')]
            docs.extend(comments)
            
            return ' '.join(docs)
            
        except Exception:
            return ""
    
    def _calculate_structure_readability(self, code: str) -> float:
        """Calculate readability based on code structure"""
        try:
            tree = ast.parse(code)
            score = 1.0
            
            # Check nesting depth
            max_depth = self._calculate_max_nesting_depth(tree)
            if max_depth > 4:
                score -= 0.2
            
            # Check function length
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    func_lines = node.end_lineno - node.lineno if hasattr(node, 'end_lineno') else 10
                    if func_lines > 50:
                        score -= 0.1
            
            return max(0.0, min(1.0, score))
            
        except Exception:
            return 0.5
    
    def _calculate_max_nesting_depth(self, node: ast.AST, current_depth: int = 0) -> int:
        """Calculate maximum nesting depth in AST"""
        max_depth = current_depth
        
        for child in ast.iter_child_nodes(node):
            if isinstance(child, (ast.If, ast.For, ast.While, ast.With, ast.Try)):
                child_depth = self._calculate_max_nesting_depth(child, current_depth + 1)
                max_depth = max(max_depth, child_depth)
            else:
                child_depth = self._calculate_max_nesting_depth(child, current_depth)
                max_depth = max(max_depth, child_depth)
        
        return max_depth
    
    def _get_performance_details(self, code: str) -> Dict[str, Any]:
        """Get detailed performance metrics"""
        return {
            "static_analysis": True,
            "dynamic_testing": self.enable_performance_testing
        }
    
    def _get_readability_details(self, code: str) -> Dict[str, Any]:
        """Get detailed readability metrics"""
        return {
            "has_comments": '#' in code,
            "has_docstrings": '"""' in code or "'''" in code,
            "line_count": len(code.split('\n'))
        }
    
    def _get_complexity_details(self, code: str) -> Dict[str, Any]:
        """Get detailed complexity metrics"""
        try:
            tree = ast.parse(code)
            details = {
                "function_count": len([n for n in ast.walk(tree) if isinstance(n, ast.FunctionDef)]),
                "class_count": len([n for n in ast.walk(tree) if isinstance(n, ast.ClassDef)]),
                "loop_count": len([n for n in ast.walk(tree) if isinstance(n, (ast.For, ast.While))]),
                "condition_count": len([n for n in ast.walk(tree) if isinstance(n, ast.If)])
            }
            return details
        except Exception:
            return {}
    
    def _get_available_tools(self) -> List[str]:
        """Get list of available analysis tools"""
        tools = []
        if pylint is not None:
            tools.append("pylint")
        if pyflakes is not None:
            tools.append("pyflakes")
        if radon_complexity is not None:
            tools.append("radon")
        if textstat is not None:
            tools.append("textstat")
        return tools
