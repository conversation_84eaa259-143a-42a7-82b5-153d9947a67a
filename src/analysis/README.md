# 分析层 (Analysis Layer)

分析层是持续进化编程工具的核心分析引擎，负责执行代码裁剪和质量评估。按照高内聚低耦合的原则设计，提供时间维度分析、空间维度分析和多维质量评估功能。

## 架构概述

分析层包含以下核心组件：

- **时间维度分析器** (`temporal.py`) - 解析 Git diff，构建时间索引树
- **空间维度分析器** (`spatial.py`) - 提取相关代码，分析代码关系
- **质量评估器** (`quality.py`) - 计算多维质量指标

## 核心组件

### 1. TemporalAnalyzer 时间维度分析器

负责分析代码随时间的变化，构建基于 Git 历史的时间索引。

```python
class TemporalAnalyzer:
    def analyze_commit_history(file_path: str) -> List[DiffAnalysis]
    def build_time_index(file_path: str, symbol: str) -> Dict[str, Any]
    def get_related_commits(file_path: str, symbol: str) -> List[str]
```

**核心功能：**
- Git diff 解析和分析
- 时间索引树构建
- 代码热点识别
- 变更频率计算
- 函数演化历史跟踪

**输出指标：**
- 提交频率 (commits_per_day)
- 平均变更分数 (avg_change_score)
- 代码热点列表 (hotspots)
- 函数变更历史 (function_history)

### 2. SpatialAnalyzer 空间维度分析器

使用 Rope、Jedi 和 Pydeps 分析代码的空间关系和依赖。

```python
class SpatialAnalyzer:
    def extract_related_code(file_path: str, symbol: str) -> CodeVariant
    def get_code_relations(file_path: str) -> List[CodeRelation]
    def _analyze_symbol(file_path: str, symbol: str) -> Dict[str, Any]
```

**分析工具集成：**
- **Rope**: 代码重构和分析
- **Jedi**: 代码补全和定义查找
- **AST**: Python 抽象语法树分析
- **Pydeps**: 模块依赖分析

**关系类型：**
- `calls` - 函数调用关系
- `imports` - 导入关系
- `inherits` - 继承关系
- `uses` - 使用关系

### 3. QualityEvaluator 质量评估器

提供多维度的代码质量评估，支持多目标优化。

```python
class QualityEvaluator:
    def evaluate_variant(variant: CodeVariant) -> CodeVariant
    def _calculate_all_metrics(code: str) -> QualityMetrics
```

**质量维度：**

#### 性能 (Performance)
- 静态性能估算
- 循环复杂度分析
- 算法效率评估
- 可选的动态性能测试

#### 可读性 (Readability)
- 文档字符串分析 (使用 textstat)
- 代码结构评估
- 注释覆盖率
- 命名规范检查

#### 复杂性 (Complexity)
- 循环复杂度 (使用 radon)
- 嵌套深度分析
- 函数长度评估
- 条件分支计数

#### 冗余度 (Redundancy)
- 重复代码检测
- 未使用变量识别
- 代码重复率计算

#### 可维护性 (Maintainability)
- 可维护性指数 (使用 radon)
- 代码结构分析
- 模块化程度评估

#### 代码风格 (Style)
- Pylint 静态分析
- Pyflakes 错误检查
- PEP 8 规范检查
- 错误和警告统计

## 使用示例

### 时间维度分析

```python
from src.analysis import TemporalAnalyzer

# 初始化分析器
temporal_analyzer = TemporalAnalyzer("/path/to/repo")

# 分析提交历史
diff_analyses = temporal_analyzer.analyze_commit_history("main.py")

# 构建时间索引
time_index = temporal_analyzer.build_time_index("main.py", "fibonacci")

# 获取相关提交
related_commits = temporal_analyzer.get_related_commits("main.py", "fibonacci", 30)
```

### 空间维度分析

```python
from src.analysis import SpatialAnalyzer

# 初始化分析器
spatial_analyzer = SpatialAnalyzer("/path/to/project")

# 提取相关代码
variant = spatial_analyzer.extract_related_code("main.py", "fibonacci")

# 获取代码关系
relations = spatial_analyzer.get_code_relations("main.py")

# 清理资源
spatial_analyzer.cleanup()
```

### 质量评估

```python
from src.analysis import QualityEvaluator
from src.data import CodeVariant

# 初始化评估器
quality_evaluator = QualityEvaluator()

# 创建代码变体
variant = CodeVariant(
    code="def fibonacci(n): return n if n <= 1 else fibonacci(n-1) + fibonacci(n-2)",
    commit_hash="abc123",
    branch="main",
    symbol="fibonacci"
)

# 评估质量
evaluated_variant = quality_evaluator.evaluate_variant(variant)

# 查看指标
print(f"Performance: {evaluated_variant.metrics['performance']}")
print(f"Readability: {evaluated_variant.metrics['readability']}")
print(f"Complexity: {evaluated_variant.metrics['complexity']}")
```

### 集成分析工作流

```python
# 完整的分析流程
def analyze_code_symbol(repo_path, file_path, symbol):
    # 1. 空间分析：提取相关代码
    spatial_analyzer = SpatialAnalyzer(repo_path)
    variant = spatial_analyzer.extract_related_code(file_path, symbol)
    
    # 2. 质量评估：评估代码质量
    quality_evaluator = QualityEvaluator()
    variant = quality_evaluator.evaluate_variant(variant)
    
    # 3. 时间分析：构建时间索引
    temporal_analyzer = TemporalAnalyzer(repo_path)
    time_index = temporal_analyzer.build_time_index(file_path, symbol)
    
    # 4. 整合结果
    variant.execution_results['time_index'] = time_index
    
    return variant
```

## 多目标优化支持

分析层为多目标优化提供标准化的目标值：

```python
# 目标值设置 (0.0 到 1.0，越高越好)
variant.objective_values = [
    metrics.performance,           # 性能
    metrics.readability,          # 可读性  
    1.0 - metrics.complexity,     # 复杂性 (反向)
    1.0 - metrics.redundancy      # 冗余度 (反向)
]
```

## 工具依赖

### 必需依赖
- `gitpython>=3.1.44` - Git 操作
- `ast` - Python 语法树分析 (内置)

### 可选依赖
- `rope>=1.13.0` - 代码重构分析
- `jedi>=0.19.2` - 代码智能分析
- `pydeps>=3.0.1` - 依赖关系分析
- `pylint>=3.3.7` - 代码风格检查
- `pyflakes>=3.3.2` - 错误检查
- `radon>=6.0.1` - 复杂度分析
- `textstat>=0.7.7` - 文本可读性分析

## 性能优化

### 缓存机制
- AST 解析结果缓存
- 时间索引数据库存储
- 分析结果复用

### 渐进式分析
- 增量 Git diff 分析
- 按需代码提取
- 分层质量评估

### 并行处理
- 多文件并行分析
- 独立指标并行计算
- 异步结果聚合

## 错误处理

### 优雅降级
- 工具缺失时使用基础分析
- 分析失败时提供默认值
- 部分失败不影响整体流程

### 异常类型
- `TemporalError` - 时间分析错误
- `SpatialError` - 空间分析错误  
- `QualityError` - 质量评估错误

## 扩展性

### 新增质量指标
```python
def _calculate_custom_metric(self, code: str) -> float:
    # 实现自定义指标计算
    return score

# 在 QualityEvaluator 中注册
metrics.custom_metric = self._calculate_custom_metric(code)
```

### 新增分析工具
```python
def _analyze_with_custom_tool(self, code: str) -> Dict[str, Any]:
    # 集成新的分析工具
    return analysis_results
```

## 测试验证

运行分析层演示：

```bash
uv run python demo_analysis_layer.py
```

## 下一步

分析层为处理层提供了丰富的分析能力：

1. **处理层** - 将使用分析结果进行变异和优化
2. **演化管理** - 将基于质量指标进行 Pareto 优化
3. **LLM 集成** - 将利用代码关系生成智能变异

分析层的设计确保了系统能够深入理解代码结构、质量和演化历史，为智能代码演化提供坚实基础。
