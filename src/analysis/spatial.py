"""
Spatial Analyzer

Handles spatial dimension analysis by extracting related code using <PERSON><PERSON>, <PERSON>i, and Pydep<PERSON>.
"""

import ast
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional

# Import rope components with proper error handling
try:
    from rope.base.project import Project
    ROPE_AVAILABLE = True
except ImportError:
    Project = None
    ROPE_AVAILABLE = False

try:
    import jedi
except ImportError:
    jedi = None

try:
    import pydeps
except ImportError:
    pydeps = None

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from data.models import CodeVariant


class SpatialError(Exception):
    """Custom exception for spatial analysis operations"""
    pass


class CodeRelation:
    """Represents a relationship between code elements"""
    
    def __init__(self, source: str, target: str, relation_type: str, 
                 strength: float, context: Optional[str] = None):
        self.source = source
        self.target = target
        self.relation_type = relation_type  # 'calls', 'imports', 'inherits', 'uses'
        self.strength = strength  # 0.0 to 1.0
        self.context = context


class SpatialAnalyzer:
    """
    Analyzes spatial relationships in code using static analysis tools.
    
    This class provides spatial analysis capabilities by extracting related code
    and understanding code dependencies and relationships.
    """
    
    def __init__(self, project_path: str):
        """
        Initialize spatial analyzer.
        
        Args:
            project_path: Path to project root
        """
        self.project_path = Path(project_path)
        self._logger = logging.getLogger(__name__)
        self._rope_project = None  # type: ignore
        self._ast_cache: Dict[str, ast.AST] = {}
    
    @property
    def rope_project(self):  # type: ignore
        """Get Rope project instance"""
        if self._rope_project is None and Project is not None:
            try:
                self._rope_project = Project(str(self.project_path))
            except Exception as e:
                self._logger.warning(f"Failed to initialize Rope project: {e}")
        return self._rope_project
    
    def extract_related_code(self, file_path: str, symbol: str) -> CodeVariant:
        """
        Extract code related to a specific symbol.
        
        Args:
            file_path: Path to file containing the symbol
            symbol: Symbol name to analyze
            
        Returns:
            CodeVariant containing extracted related code
        """
        try:
            self._logger.info(f"Extracting related code for symbol '{symbol}' in {file_path}")
            
            # Get symbol definition and related code
            symbol_info = self._analyze_symbol(file_path, symbol)
            related_symbols = self._find_related_symbols(file_path, symbol)
            dependencies = self._analyze_dependencies(file_path, symbol)
            
            # Extract code snippets
            code_snippets = []
            
            # Add main symbol code
            if symbol_info and symbol_info.get('code'):
                code_snippets.append(f"# Main symbol: {symbol}")
                code_snippets.append(symbol_info['code'])
                code_snippets.append("")
            
            # Add related symbols
            for related_symbol, relation in related_symbols.items():
                if relation.get('code'):
                    code_snippets.append(f"# Related symbol: {related_symbol} ({relation['type']})")
                    code_snippets.append(relation['code'])
                    code_snippets.append("")
            
            # Combine all code
            extracted_code = "\n".join(code_snippets)
            
            # Create variant
            variant = CodeVariant(
                code=extracted_code,
                commit_hash="",  # Will be set later
                branch="spatial_analysis",
                file_path=file_path,
                symbol=symbol,
                metrics={
                    "related_symbols_count": len(related_symbols),
                    "dependencies_count": len(dependencies),
                    "code_lines": len(extracted_code.split('\n')),
                    "complexity_score": self._calculate_complexity_score(symbol_info, related_symbols)
                },
                execution_results={
                    "symbol_info": symbol_info,
                    "related_symbols": related_symbols,
                    "dependencies": dependencies,
                    "spatial_analysis": True
                }
            )
            
            self._logger.info(f"Extracted {len(related_symbols)} related symbols for '{symbol}'")
            return variant
            
        except Exception as e:
            raise SpatialError(f"Failed to extract related code: {e}")
    
    def _analyze_symbol(self, file_path: str, symbol: str) -> Dict[str, Any]:
        """
        Analyze a specific symbol using multiple tools.
        
        Args:
            file_path: Path to file
            symbol: Symbol name
            
        Returns:
            Dictionary with symbol information
        """
        symbol_info = {
            "name": symbol,
            "type": "unknown",
            "line_number": None,
            "code": "",
            "docstring": None,
            "parameters": [],
            "return_type": None
        }
        
        try:
            # Use AST analysis
            ast_info = self._analyze_symbol_with_ast(file_path, symbol)
            symbol_info.update(ast_info)
            
            # Use Jedi if available
            if jedi:
                jedi_info = self._analyze_symbol_with_jedi(file_path, symbol)
                symbol_info.update(jedi_info)
            
            # Use Rope if available
            if self.rope_project:
                rope_info = self._analyze_symbol_with_rope(file_path, symbol)
                symbol_info.update(rope_info)
                
        except Exception as e:
            self._logger.warning(f"Failed to analyze symbol {symbol}: {e}")
        
        return symbol_info
    
    def _analyze_symbol_with_ast(self, file_path: str, symbol: str) -> Dict[str, Any]:
        """Analyze symbol using AST"""
        info = {}
        
        try:
            # Get or parse AST
            if file_path not in self._ast_cache:
                with open(self.project_path / file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                self._ast_cache[file_path] = ast.parse(content)
            
            tree = self._ast_cache[file_path]
            
            # Find symbol definition
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef) and node.name == symbol:
                    info.update({
                        "type": "function",
                        "line_number": node.lineno,
                        "code": self._extract_node_code(file_path, node),
                        "docstring": ast.get_docstring(node),
                        "parameters": [arg.arg for arg in node.args.args]
                    })
                    break
                elif isinstance(node, ast.ClassDef) and node.name == symbol:
                    info.update({
                        "type": "class",
                        "line_number": node.lineno,
                        "code": self._extract_node_code(file_path, node),
                        "docstring": ast.get_docstring(node),
                        "methods": [n.name for n in node.body if isinstance(n, ast.FunctionDef)]
                    })
                    break
                elif isinstance(node, ast.Assign):
                    for target in node.targets:
                        if isinstance(target, ast.Name) and target.id == symbol:
                            info.update({
                                "type": "variable",
                                "line_number": node.lineno,
                                "code": self._extract_node_code(file_path, node)
                            })
                            break
                            
        except Exception as e:
            self._logger.warning(f"AST analysis failed for {symbol}: {e}")
        
        return info
    
    def _analyze_symbol_with_jedi(self, file_path: str, symbol: str) -> Dict[str, Any]:
        """Analyze symbol using Jedi"""
        info = {}
        
        try:
            # Read file content
            with open(self.project_path / file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Use Jedi to get definitions
            if jedi is not None:
                script = jedi.Script(code=content, path=str(self.project_path / file_path))
            else:
                return info
            definitions = script.goto(line=1, column=0, follow_imports=True)
            
            for definition in definitions:
                if definition.name == symbol:
                    info.update({
                        "jedi_type": definition.type,
                        "module_path": definition.module_path,
                        "full_name": definition.full_name,
                        "description": definition.description
                    })
                    break
                    
        except Exception as e:
            self._logger.warning(f"Jedi analysis failed for {symbol}: {e}")
        
        return info
    
    def _analyze_symbol_with_rope(self, _file_path: str, _symbol: str) -> Dict[str, Any]:
        """Analyze symbol using Rope"""
        info = {}
        
        try:
            if not self.rope_project:
                return info
            
            # Simplified rope analysis
            info.update({
                "rope_analysis": True,
                "rope_available": ROPE_AVAILABLE
            })
            
        except Exception as e:
            self._logger.warning(f"Rope analysis failed for {_symbol}: {e}")
        
        return info
    
    def _extract_node_code(self, file_path: str, node: ast.AST) -> str:
        """Extract source code for an AST node"""
        try:
            with open(self.project_path / file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # Get node lines (simplified approach)
            start_line = getattr(node, 'lineno', 1) - 1

            # Find end line by looking for next node at same level
            end_line = len(lines)
            if hasattr(node, 'end_lineno') and getattr(node, 'end_lineno', None):
                end_line = getattr(node, 'end_lineno', len(lines))
            else:
                # Estimate end line based on indentation
                if start_line < len(lines):
                    base_indent = len(lines[start_line]) - len(lines[start_line].lstrip())
                    for i in range(start_line + 1, len(lines)):
                        line = lines[i]
                        if line.strip() and len(line) - len(line.lstrip()) <= base_indent:
                            end_line = i
                            break
            
            return "".join(lines[start_line:end_line])
            
        except Exception as e:
            self._logger.warning(f"Failed to extract code for node: {e}")
            return ""
    
    def _find_related_symbols(self, file_path: str, _symbol: str) -> Dict[str, Dict[str, Any]]:
        """Find symbols related to the target symbol"""
        related = {}
        
        try:
            # Use AST to find relationships
            if file_path in self._ast_cache:
                tree = self._ast_cache[file_path]
            else:
                with open(self.project_path / file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                tree = ast.parse(content)
                self._ast_cache[file_path] = tree
            
            # Find function calls, imports, etc.
            for node in ast.walk(tree):
                if isinstance(node, ast.Call):
                    # Function calls
                    if isinstance(node.func, ast.Name):
                        related[node.func.id] = {
                            "type": "function_call",
                            "line": node.lineno,
                            "code": self._extract_node_code(file_path, node)
                        }
                elif isinstance(node, ast.Import):
                    # Import statements
                    for alias in node.names:
                        related[alias.name] = {
                            "type": "import",
                            "line": node.lineno,
                            "code": self._extract_node_code(file_path, node)
                        }
                elif isinstance(node, ast.ImportFrom):
                    # From imports
                    if node.module:
                        for alias in node.names:
                            related[f"{node.module}.{alias.name}"] = {
                                "type": "from_import",
                                "line": node.lineno,
                                "code": self._extract_node_code(file_path, node)
                            }
            
        except Exception as e:
            self._logger.warning(f"Failed to find related symbols: {e}")
        
        return related
    
    def _analyze_dependencies(self, file_path: str, _symbol: str) -> List[Dict[str, Any]]:
        """Analyze dependencies using pydeps if available"""
        dependencies = []
        
        try:
            if pydeps is None:
                return dependencies
            
            # Use pydeps to analyze module dependencies
            # Note: This is a simplified approach
            # For now, return basic dependency info
            dependencies.append({
                "type": "module_dependency",
                "target": file_path,
                "analysis_tool": "pydeps"
            })
            
        except Exception as e:
            self._logger.warning(f"Dependency analysis failed: {e}")
        
        return dependencies
    
    def _calculate_complexity_score(self, symbol_info: Dict[str, Any], 
                                   related_symbols: Dict[str, Dict[str, Any]]) -> float:
        """Calculate complexity score based on relationships"""
        score = 0.0
        
        # Base score from symbol type
        if symbol_info.get("type") == "function":
            score += 1.0
            score += len(symbol_info.get("parameters", [])) * 0.1
        elif symbol_info.get("type") == "class":
            score += 2.0
            score += len(symbol_info.get("methods", [])) * 0.2
        
        # Add score for related symbols
        score += len(related_symbols) * 0.1
        
        # Add score for different relationship types
        relation_types = set(rel.get("type", "") for rel in related_symbols.values())
        score += len(relation_types) * 0.2
        
        return min(score, 10.0)  # Cap at 10.0
    
    def get_code_relations(self, file_path: str) -> List[CodeRelation]:
        """
        Get all code relations in a file.
        
        Args:
            file_path: Path to file
            
        Returns:
            List of code relations
        """
        relations = []
        
        try:
            # Parse file with AST
            with open(self.project_path / file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            tree = ast.parse(content)
            
            # Extract relations
            for node in ast.walk(tree):
                if isinstance(node, ast.Call) and isinstance(node.func, ast.Name):
                    # Function call relation
                    relations.append(CodeRelation(
                        source="current_context",
                        target=node.func.id,
                        relation_type="calls",
                        strength=0.8
                    ))
                elif isinstance(node, ast.Import):
                    # Import relation
                    for alias in node.names:
                        relations.append(CodeRelation(
                            source=file_path,
                            target=alias.name,
                            relation_type="imports",
                            strength=0.6
                        ))
            
        except Exception as e:
            self._logger.warning(f"Failed to get code relations: {e}")
        
        return relations
    
    def cleanup(self):
        """Cleanup resources"""
        if self._rope_project:
            self._rope_project.close()
        self._ast_cache.clear()
