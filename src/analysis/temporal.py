"""
Temporal Analyzer

Handles time dimension analysis by parsing Git diff and building time index trees.
"""

import logging
from pathlib import Path
from typing import List, Dict, Any, Optional, Set
from datetime import datetime
from git import Repo, Commit, Diff
from git.exc import GitCommandError

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from data.models import CodeVariant
from data.database import DatabaseManager


class TemporalError(Exception):
    """Custom exception for temporal analysis operations"""
    pass


class DiffAnalysis:
    """Represents analysis of a Git diff"""
    
    def __init__(self, commit_hash: str, timestamp: str, file_path: str, 
                 added_lines: List[str], removed_lines: List[str], 
                 modified_functions: Set[str]):
        self.commit_hash = commit_hash
        self.timestamp = timestamp
        self.file_path = file_path
        self.added_lines = added_lines
        self.removed_lines = removed_lines
        self.modified_functions = modified_functions
        self.change_score = len(added_lines) + len(removed_lines)


class TemporalAnalyzer:
    """
    Analyzes code changes over time using Git history.
    
    This class provides temporal analysis capabilities by parsing Git diffs
    and building time-based indices for code evolution tracking.
    """
    
    def __init__(self, repo_path: str, db_manager: Optional[DatabaseManager] = None):
        """
        Initialize temporal analyzer.
        
        Args:
            repo_path: Path to Git repository
            db_manager: Optional database manager for storing indices
        """
        self.repo_path = Path(repo_path)
        self.db_manager = db_manager
        self._logger = logging.getLogger(__name__)
        self._repo: Optional[Repo] = None
    
    @property
    def repo(self) -> Repo:
        """Get Git repository instance"""
        if self._repo is None:
            try:
                self._repo = Repo(self.repo_path)
            except Exception as e:
                raise TemporalError(f"Failed to access repository: {e}")
        return self._repo
    
    def analyze_commit_history(self, file_path: str, max_commits: int = 100) -> List[DiffAnalysis]:
        """
        Analyze commit history for a specific file.
        
        Args:
            file_path: Path to file relative to repository root
            max_commits: Maximum number of commits to analyze
            
        Returns:
            List of diff analyses ordered by time (newest first)
        """
        try:
            self._logger.info(f"Analyzing commit history for {file_path}")
            
            # Get commits that modified the file
            commits = list(self.repo.iter_commits(paths=file_path, max_count=max_commits))
            
            if not commits:
                self._logger.warning(f"No commits found for file: {file_path}")
                return []
            
            diff_analyses = []
            
            for i, commit in enumerate(commits):
                try:
                    # Get diff for this commit
                    if i < len(commits) - 1:
                        # Compare with previous commit
                        parent_commit = commits[i + 1]
                        diffs = commit.diff(parent_commit, paths=file_path)
                    else:
                        # First commit, compare with empty
                        diffs = commit.diff(None, paths=file_path)
                    
                    for diff in diffs:
                        if diff.a_path == file_path or diff.b_path == file_path:
                            analysis = self._analyze_diff(commit, diff)
                            if analysis:
                                diff_analyses.append(analysis)
                
                except Exception as e:
                    self._logger.warning(f"Failed to analyze commit {commit.hexsha}: {e}")
                    continue
            
            self._logger.info(f"Analyzed {len(diff_analyses)} diffs for {file_path}")
            return diff_analyses
            
        except GitCommandError as e:
            raise TemporalError(f"Git operation failed: {e}")
    
    def _analyze_diff(self, commit: Commit, diff: Diff) -> Optional[DiffAnalysis]:
        """
        Analyze a single Git diff.
        
        Args:
            commit: Git commit object
            diff: Git diff object
            
        Returns:
            DiffAnalysis object or None if analysis fails
        """
        try:
            file_path = diff.b_path or diff.a_path
            if not file_path:
                return None
            
            # Get diff content
            if diff.diff:
                if isinstance(diff.diff, bytes):
                    diff_text = diff.diff.decode('utf-8', errors='ignore')
                else:
                    diff_text = str(diff.diff)
            else:
                diff_text = ""
            
            # Parse added and removed lines
            added_lines = []
            removed_lines = []
            
            for line in diff_text.split('\n'):
                if line.startswith('+') and not line.startswith('+++'):
                    added_lines.append(line[1:])  # Remove '+' prefix
                elif line.startswith('-') and not line.startswith('---'):
                    removed_lines.append(line[1:])  # Remove '-' prefix
            
            # Identify modified functions (simple heuristic)
            modified_functions = self._identify_modified_functions(added_lines + removed_lines)
            
            return DiffAnalysis(
                commit_hash=commit.hexsha,
                timestamp=commit.committed_datetime.isoformat(),
                file_path=file_path,
                added_lines=added_lines,
                removed_lines=removed_lines,
                modified_functions=modified_functions
            )
            
        except Exception as e:
            self._logger.warning(f"Failed to analyze diff: {e}")
            return None
    
    def _identify_modified_functions(self, lines: List[str]) -> Set[str]:
        """
        Identify function names from modified lines.
        
        Args:
            lines: List of code lines
            
        Returns:
            Set of function names
        """
        functions = set()
        
        for line in lines:
            line = line.strip()
            
            # Python function definition
            if line.startswith('def ') and '(' in line:
                func_name = line.split('def ')[1].split('(')[0].strip()
                if func_name.isidentifier():
                    functions.add(func_name)
            
            # Python class definition
            elif line.startswith('class ') and ':' in line:
                class_name = line.split('class ')[1].split(':')[0].split('(')[0].strip()
                if class_name.isidentifier():
                    functions.add(class_name)
        
        return functions
    
    def build_time_index(self, file_path: str, symbol: Optional[str] = None) -> Dict[str, Any]:
        """
        Build time-based index for code changes.
        
        Args:
            file_path: Path to file
            symbol: Optional specific symbol to track
            
        Returns:
            Time index dictionary
        """
        try:
            self._logger.info(f"Building time index for {file_path}")
            
            # Analyze commit history
            diff_analyses = self.analyze_commit_history(file_path)
            
            # Build index structure
            time_index = {
                "file_path": file_path,
                "symbol": symbol,
                "total_commits": len(diff_analyses),
                "first_commit": diff_analyses[-1].timestamp if diff_analyses else None,
                "last_commit": diff_analyses[0].timestamp if diff_analyses else None,
                "commits": [],
                "function_history": {},
                "change_frequency": self._calculate_change_frequency(diff_analyses),
                "hotspots": self._identify_hotspots(diff_analyses)
            }
            
            # Process each commit
            for analysis in diff_analyses:
                commit_info = {
                    "hash": analysis.commit_hash,
                    "timestamp": analysis.timestamp,
                    "added_lines": len(analysis.added_lines),
                    "removed_lines": len(analysis.removed_lines),
                    "change_score": analysis.change_score,
                    "modified_functions": list(analysis.modified_functions)
                }
                time_index["commits"].append(commit_info)
                
                # Track function history
                for func_name in analysis.modified_functions:
                    if func_name not in time_index["function_history"]:
                        time_index["function_history"][func_name] = []
                    
                    time_index["function_history"][func_name].append({
                        "commit": analysis.commit_hash,
                        "timestamp": analysis.timestamp,
                        "change_score": analysis.change_score
                    })
            
            # Store in database if available
            if self.db_manager:
                self._store_time_index(time_index)
            
            self._logger.info(f"Built time index with {len(diff_analyses)} commits")
            return time_index
            
        except Exception as e:
            raise TemporalError(f"Failed to build time index: {e}")
    
    def _calculate_change_frequency(self, diff_analyses: List[DiffAnalysis]) -> Dict[str, float]:
        """Calculate change frequency metrics"""
        if not diff_analyses:
            return {"commits_per_day": 0.0, "avg_change_score": 0.0}
        
        # Calculate time span
        timestamps = []
        for analysis in diff_analyses:
            timestamp_str = analysis.timestamp
            # Handle different timestamp formats
            if timestamp_str.endswith('Z'):
                timestamp_str = timestamp_str.replace('Z', '+00:00')
            elif '+' in timestamp_str and timestamp_str.count('+') > 1:
                # Handle double timezone format like '2025-06-13T11:15:44-07:00+00:00'
                parts = timestamp_str.split('+')
                if len(parts) > 2:
                    timestamp_str = '+'.join(parts[:-1])
            elif '+' not in timestamp_str and '-' not in timestamp_str[-6:] and 'T' in timestamp_str:
                timestamp_str += '+00:00'

            try:
                timestamps.append(datetime.fromisoformat(timestamp_str))
            except ValueError:
                # Fallback: use current time if parsing fails
                timestamps.append(datetime.now())
        time_span = (max(timestamps) - min(timestamps)).days
        
        # Calculate metrics
        commits_per_day = len(diff_analyses) / max(time_span, 1)
        avg_change_score = sum(analysis.change_score for analysis in diff_analyses) / len(diff_analyses)
        
        return {
            "commits_per_day": commits_per_day,
            "avg_change_score": avg_change_score,
            "total_changes": sum(analysis.change_score for analysis in diff_analyses)
        }
    
    def _identify_hotspots(self, diff_analyses: List[DiffAnalysis]) -> List[Dict[str, Any]]:
        """Identify code hotspots (frequently changed functions)"""
        function_changes = {}
        
        for analysis in diff_analyses:
            for func_name in analysis.modified_functions:
                if func_name not in function_changes:
                    function_changes[func_name] = {"count": 0, "total_score": 0}
                
                function_changes[func_name]["count"] += 1
                function_changes[func_name]["total_score"] += analysis.change_score
        
        # Sort by change frequency
        hotspots = []
        for func_name, stats in function_changes.items():
            hotspots.append({
                "function": func_name,
                "change_count": stats["count"],
                "total_change_score": stats["total_score"],
                "avg_change_score": stats["total_score"] / stats["count"]
            })
        
        # Sort by change count (descending)
        hotspots.sort(key=lambda x: x["change_count"], reverse=True)
        
        return hotspots[:10]  # Top 10 hotspots
    
    def _store_time_index(self, time_index: Dict[str, Any]) -> None:
        """Store time index in database"""
        if not self.db_manager:
            return
        
        try:
            # Create a variant to store the time index
            variant = CodeVariant(
                code="",  # No code content for index
                commit_hash=time_index["commits"][0]["hash"] if time_index["commits"] else "index",
                branch="time_index",
                file_path=time_index["file_path"],
                symbol=time_index["symbol"],
                metrics={
                    "total_commits": time_index["total_commits"],
                    "change_frequency": time_index["change_frequency"]["commits_per_day"],
                    "avg_change_score": time_index["change_frequency"]["avg_change_score"]
                },
                execution_results={"time_index": time_index}
            )
            
            self.db_manager.store_variant(variant)
            self._logger.info("Stored time index in database")
            
        except Exception as e:
            self._logger.warning(f"Failed to store time index: {e}")
    
    def query_time_index(self, file_path: str, symbol: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        Query existing time index from database.
        
        Args:
            file_path: Path to file
            symbol: Optional symbol filter
            
        Returns:
            Time index dictionary or None if not found
        """
        if not self.db_manager:
            return None
        
        try:
            variants = self.db_manager.query_variants(branch="time_index")
            
            for variant in variants:
                if (variant.file_path == file_path and 
                    (symbol is None or variant.symbol == symbol)):
                    return variant.execution_results.get("time_index")
            
            return None
            
        except Exception as e:
            self._logger.warning(f"Failed to query time index: {e}")
            return None
    
    def get_related_commits(self, file_path: str, symbol: str, 
                           time_window_days: int = 30) -> List[str]:
        """
        Get commits related to a specific symbol within a time window.
        
        Args:
            file_path: Path to file
            symbol: Symbol name
            time_window_days: Time window in days
            
        Returns:
            List of commit hashes
        """
        try:
            time_index = self.query_time_index(file_path, symbol)
            if not time_index:
                # Build index if not exists
                time_index = self.build_time_index(file_path, symbol)
            
            # Filter commits by time window and symbol
            cutoff_time = datetime.now().timestamp() - (time_window_days * 24 * 60 * 60)
            related_commits = []
            
            function_history = time_index.get("function_history", {}).get(symbol, [])
            
            for entry in function_history:
                commit_time = datetime.fromisoformat(entry["timestamp"]).timestamp()
                if commit_time >= cutoff_time:
                    related_commits.append(entry["commit"])
            
            return related_commits
            
        except Exception as e:
            self._logger.warning(f"Failed to get related commits: {e}")
            return []
