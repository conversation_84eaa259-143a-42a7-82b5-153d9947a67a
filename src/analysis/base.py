"""
Base Analyzer

Provides base class and common interfaces for all analyzers.
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, List
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from data.models import CodeVariant


class AnalysisError(Exception):
    """Base exception for analysis operations"""
    pass


class BaseAnalyzer(ABC):
    """
    Base class for all analyzers.
    
    Provides common interface and functionality for temporal, spatial,
    and quality analyzers following the principle of high cohesion and low coupling.
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize base analyzer.
        
        Args:
            config: Optional configuration dictionary
        """
        self.config = config or {}
        self._logger = logging.getLogger(self.__class__.__name__)
    
    @abstractmethod
    def analyze(self, variant: CodeVariant, **kwargs) -> CodeVariant:
        """
        Execute analysis and return enhanced variant.
        
        Args:
            variant: Code variant to analyze
            **kwargs: Additional analysis parameters
            
        Returns:
            Enhanced code variant with analysis results
            
        Raises:
            AnalysisError: If analysis fails
        """
        pass
    
    def get_supported_metrics(self) -> List[str]:
        """
        Get list of metrics supported by this analyzer.
        
        Returns:
            List of metric names
        """
        return []
    
    def get_analysis_type(self) -> str:
        """
        Get the type of analysis performed by this analyzer.
        
        Returns:
            Analysis type name
        """
        return self.__class__.__name__.replace('Analyzer', '').replace('Evaluator', '').lower()
    
    def validate_input(self, variant: CodeVariant) -> bool:
        """
        Validate input variant for analysis.
        
        Args:
            variant: Code variant to validate
            
        Returns:
            True if variant is valid for analysis
        """
        if not variant:
            return False
        
        if not variant.code or not variant.code.strip():
            return False
        
        return True
    
    def prepare_variant(self, variant: CodeVariant) -> CodeVariant:
        """
        Prepare variant for analysis (add metadata, etc.).
        
        Args:
            variant: Input variant
            
        Returns:
            Prepared variant
        """
        # Ensure execution_results exists
        if not hasattr(variant, 'execution_results') or variant.execution_results is None:
            variant.execution_results = {}
        
        # Add analysis metadata
        analysis_type = self.get_analysis_type()
        if 'analysis_history' not in variant.execution_results:
            variant.execution_results['analysis_history'] = []
        
        variant.execution_results['analysis_history'].append({
            'analyzer': analysis_type,
            'timestamp': self._get_timestamp(),
            'config': self.config.copy()
        })
        
        return variant
    
    def finalize_variant(self, variant: CodeVariant, analysis_results: Dict[str, Any]) -> CodeVariant:
        """
        Finalize variant after analysis (add results, update metrics).
        
        Args:
            variant: Analyzed variant
            analysis_results: Analysis results to add
            
        Returns:
            Finalized variant
        """
        analysis_type = self.get_analysis_type()
        
        # Store analysis results
        variant.execution_results[f'{analysis_type}_analysis'] = analysis_results
        
        # Update metrics if provided
        if 'metrics' in analysis_results:
            variant.update_metrics(analysis_results['metrics'])
        
        # Log completion
        self._logger.info(f"{analysis_type.title()} analysis completed for {variant.symbol}")
        
        return variant
    
    def handle_analysis_error(self, variant: CodeVariant, error: Exception) -> CodeVariant:
        """
        Handle analysis errors gracefully.
        
        Args:
            variant: Variant being analyzed
            error: Error that occurred
            
        Returns:
            Variant with error information
        """
        analysis_type = self.get_analysis_type()
        
        self._logger.error(f"{analysis_type.title()} analysis failed: {error}")
        
        # Add error information to variant
        variant.execution_results[f'{analysis_type}_analysis'] = {
            'success': False,
            'error': str(error),
            'error_type': type(error).__name__,
            'timestamp': self._get_timestamp()
        }
        
        # Set default metrics to prevent downstream failures
        default_metrics = self._get_default_metrics()
        variant.update_metrics(default_metrics)
        
        return variant
    
    def _get_timestamp(self) -> str:
        """Get current timestamp in ISO format"""
        from datetime import datetime
        return datetime.now().isoformat()
    
    def _get_default_metrics(self) -> Dict[str, float]:
        """
        Get default metrics for this analyzer type.
        
        Returns:
            Dictionary of default metric values
        """
        return {}
    
    def get_config_value(self, key: str, default: Any = None) -> Any:
        """
        Get configuration value with fallback.
        
        Args:
            key: Configuration key
            default: Default value if key not found
            
        Returns:
            Configuration value
        """
        return self.config.get(key, default)
    
    def set_config_value(self, key: str, value: Any) -> None:
        """
        Set configuration value.
        
        Args:
            key: Configuration key
            value: Value to set
        """
        self.config[key] = value
    
    def cleanup(self) -> None:
        """
        Cleanup resources used by analyzer.
        
        Override in subclasses if cleanup is needed.
        """
        pass


class AnalysisResult:
    """
    Container for analysis results.
    
    Provides a structured way to return analysis results
    with metadata and metrics.
    """
    
    def __init__(self, success: bool = True, metrics: Dict[str, float] = None, 
                 data: Dict[str, Any] = None, error: str = None):
        """
        Initialize analysis result.
        
        Args:
            success: Whether analysis was successful
            metrics: Calculated metrics
            data: Additional analysis data
            error: Error message if analysis failed
        """
        self.success = success
        self.metrics = metrics or {}
        self.data = data or {}
        self.error = error
        self.timestamp = self._get_timestamp()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert result to dictionary"""
        return {
            'success': self.success,
            'metrics': self.metrics,
            'data': self.data,
            'error': self.error,
            'timestamp': self.timestamp
        }
    
    def _get_timestamp(self) -> str:
        """Get current timestamp"""
        from datetime import datetime
        return datetime.now().isoformat()


class AnalysisConfig:
    """
    Unified configuration management for analyzers.
    
    Provides centralized configuration with type-specific sections.
    """
    
    def __init__(self):
        """Initialize with default configuration"""
        self.config = {
            'temporal': {
                'max_commits': 100,
                'time_window_days': 30,
                'enable_hotspot_detection': True
            },
            'spatial': {
                'enable_rope': True,
                'enable_jedi': True,
                'max_related_symbols': 50,
                'complexity_threshold': 10.0
            },
            'quality': {
                'enable_performance_testing': False,
                'enable_pylint': True,
                'enable_pyflakes': True,
                'enable_radon': True,
                'enable_textstat': True
            },
            'global': {
                'timeout_seconds': 300,
                'max_memory_mb': 1024,
                'log_level': 'INFO'
            }
        }
    
    def get_analyzer_config(self, analyzer_type: str) -> Dict[str, Any]:
        """
        Get configuration for specific analyzer type.
        
        Args:
            analyzer_type: Type of analyzer ('temporal', 'spatial', 'quality')
            
        Returns:
            Configuration dictionary for the analyzer
        """
        analyzer_config = self.config.get(analyzer_type, {}).copy()
        analyzer_config.update(self.config.get('global', {}))
        return analyzer_config
    
    def set_analyzer_config(self, analyzer_type: str, config: Dict[str, Any]) -> None:
        """
        Set configuration for specific analyzer type.
        
        Args:
            analyzer_type: Type of analyzer
            config: Configuration dictionary
        """
        if analyzer_type not in self.config:
            self.config[analyzer_type] = {}
        self.config[analyzer_type].update(config)
    
    def get_global_config(self) -> Dict[str, Any]:
        """Get global configuration"""
        return self.config.get('global', {}).copy()
    
    def set_global_config(self, config: Dict[str, Any]) -> None:
        """Set global configuration"""
        self.config['global'] = config
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary"""
        return self.config.copy()
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'AnalysisConfig':
        """Create configuration from dictionary"""
        instance = cls()
        instance.config = config_dict
        return instance
