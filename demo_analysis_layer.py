#!/usr/bin/env python3
"""
Demo script for the analysis layer functionality.

This script demonstrates the usage of the analysis layer components
including TemporalAnalyzer, SpatialAnalyzer, and QualityEvaluator.
"""

import sys
import tempfile
import shutil
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.data.models import CodeVariant
from src.data.repository import RepositoryManager
from src.data.database import DatabaseManager
from src.analysis.temporal import TemporalAnalyzer
from src.analysis.spatial import SpatialAnalyzer
from src.analysis.quality import QualityEvaluator


def create_sample_code_files(repo_path: Path):
    """Create sample Python files for analysis"""
    
    # Create main module
    main_file = repo_path / "main.py"
    main_file.write_text('''
"""
Main module for demonstration.
"""

import math
from utils import helper_function


def fibonacci(n):
    """
    Calculate fibonacci number recursively.
    
    Args:
        n: Input number
        
    Returns:
        Fibonacci number
    """
    if n <= 1:
        return n
    return fibonacci(n - 1) + fi<PERSON><PERSON><PERSON>(n - 2)


def fibonacci_optimized(n):
    """
    Calculate fibonacci number iteratively (optimized).
    
    Args:
        n: Input number
        
    Returns:
        Fibonacci number
    """
    if n <= 1:
        return n
    
    a, b = 0, 1
    for _ in range(2, n + 1):
        a, b = b, a + b
    return b


def calculate_area(radius):
    """Calculate circle area."""
    return math.pi * radius ** 2


def main():
    """Main function."""
    print("Fibonacci(10):", fibonacci(10))
    print("Fibonacci optimized(10):", fibonacci_optimized(10))
    print("Circle area (r=5):", calculate_area(5))
    
    # Use helper function
    result = helper_function("test")
    print("Helper result:", result)


if __name__ == "__main__":
    main()
''')
    
    # Create utils module
    utils_file = repo_path / "utils.py"
    utils_file.write_text('''
"""
Utility functions.
"""

import re


def helper_function(text):
    """
    Process text input.
    
    Args:
        text: Input text
        
    Returns:
        Processed text
    """
    # Remove special characters
    cleaned = re.sub(r'[^a-zA-Z0-9\\s]', '', text)
    return cleaned.upper()


def validate_input(value):
    """Validate input value."""
    if not isinstance(value, (int, float)):
        raise ValueError("Input must be a number")
    return value > 0


class DataProcessor:
    """Data processing utility class."""
    
    def __init__(self, name):
        self.name = name
        self.processed_count = 0
    
    def process_data(self, data):
        """Process data items."""
        results = []
        for item in data:
            if validate_input(item):
                results.append(item * 2)
                self.processed_count += 1
        return results
    
    def get_stats(self):
        """Get processing statistics."""
        return {
            "name": self.name,
            "processed_count": self.processed_count
        }
''')


def demo_temporal_analyzer():
    """Demonstrate TemporalAnalyzer functionality"""
    print("=== TemporalAnalyzer Demo ===")
    
    # Create temporary repository
    temp_dir = tempfile.mkdtemp()
    repo_path = Path(temp_dir) / "demo_repo"
    
    try:
        # Initialize repository and create sample files
        repo_manager = RepositoryManager(str(repo_path))
        repo_manager.init_repo(str(repo_path))
        
        create_sample_code_files(repo_path)
        
        # Add and commit files
        repo_manager.repo.index.add(["main.py", "utils.py"])
        repo_manager.repo.index.commit("Initial commit with sample code")
        
        # Modify main.py to create history
        main_file = repo_path / "main.py"
        content = main_file.read_text()
        modified_content = content.replace(
            'print("Fibonacci(10):", fibonacci(10))',
            'print("Fibonacci(15):", fibonacci(15))'
        )
        main_file.write_text(modified_content)
        
        repo_manager.repo.index.add(["main.py"])
        repo_manager.repo.index.commit("Update fibonacci call to use 15")
        
        # Initialize temporal analyzer
        temporal_analyzer = TemporalAnalyzer(str(repo_path))
        
        # Analyze commit history
        diff_analyses = temporal_analyzer.analyze_commit_history("main.py")
        print(f"Found {len(diff_analyses)} commits for main.py")
        
        for analysis in diff_analyses:
            print(f"  Commit {analysis.commit_hash[:8]}: {analysis.change_score} changes")
            print(f"    Modified functions: {analysis.modified_functions}")
        
        # Build time index
        time_index = temporal_analyzer.build_time_index("main.py", "fibonacci")
        print(f"Time index built with {time_index['total_commits']} commits")
        print(f"Change frequency: {time_index['change_frequency']['commits_per_day']:.2f} commits/day")
        
        if time_index['hotspots']:
            print("Code hotspots:")
            for hotspot in time_index['hotspots'][:3]:
                print(f"  {hotspot['function']}: {hotspot['change_count']} changes")
        
        print("Temporal analysis: ✓ PASSED")
        
    except Exception as e:
        print(f"Temporal analysis error: {e}")
    finally:
        shutil.rmtree(temp_dir, ignore_errors=True)
    
    print()


def demo_spatial_analyzer():
    """Demonstrate SpatialAnalyzer functionality"""
    print("=== SpatialAnalyzer Demo ===")
    
    # Create temporary project
    temp_dir = tempfile.mkdtemp()
    project_path = Path(temp_dir) / "demo_project"
    project_path.mkdir()
    
    try:
        create_sample_code_files(project_path)
        
        # Initialize spatial analyzer
        spatial_analyzer = SpatialAnalyzer(str(project_path))
        
        # Extract related code for fibonacci function
        variant = spatial_analyzer.extract_related_code("main.py", "fibonacci")
        
        print(f"Extracted code for symbol: {variant.symbol}")
        print(f"Related symbols count: {variant.metrics['related_symbols_count']}")
        print(f"Dependencies count: {variant.metrics['dependencies_count']}")
        print(f"Code lines: {variant.metrics['code_lines']}")
        print(f"Complexity score: {variant.metrics['complexity_score']:.2f}")
        
        # Show some extracted code
        code_lines = variant.code.split('\n')[:10]  # First 10 lines
        print("Extracted code preview:")
        for i, line in enumerate(code_lines, 1):
            if line.strip():
                print(f"  {i:2d}: {line}")
        
        # Get code relations
        relations = spatial_analyzer.get_code_relations("main.py")
        print(f"Found {len(relations)} code relations")
        
        for relation in relations[:5]:  # Show first 5
            print(f"  {relation.source} --{relation.relation_type}--> {relation.target}")
        
        # Cleanup
        spatial_analyzer.cleanup()
        
        print("Spatial analysis: ✓ PASSED")
        
    except Exception as e:
        print(f"Spatial analysis error: {e}")
    finally:
        shutil.rmtree(temp_dir, ignore_errors=True)
    
    print()


def demo_quality_evaluator():
    """Demonstrate QualityEvaluator functionality"""
    print("=== QualityEvaluator Demo ===")
    
    try:
        # Initialize quality evaluator
        quality_evaluator = QualityEvaluator(enable_performance_testing=False)
        
        # Create test variants with different quality levels
        test_variants = [
            # High quality code
            CodeVariant(
                code='''
def calculate_factorial(n):
    """
    Calculate factorial of a number.
    
    Args:
        n (int): Input number
        
    Returns:
        int: Factorial of n
    """
    if n < 0:
        raise ValueError("Factorial is not defined for negative numbers")
    if n <= 1:
        return 1
    return n * calculate_factorial(n - 1)
''',
                commit_hash="test1",
                branch="main",
                symbol="calculate_factorial"
            ),
            
            # Medium quality code
            CodeVariant(
                code='''
def fib(n):
    if n<=1:
        return n
    else:
        return fib(n-1)+fib(n-2)
''',
                commit_hash="test2",
                branch="main",
                symbol="fib"
            ),
            
            # Lower quality code
            CodeVariant(
                code='''
def x(a,b,c,d,e,f,g,h,i,j):
    if a>b:
        if c>d:
            if e>f:
                if g>h:
                    if i>j:
                        return a+b+c+d+e+f+g+h+i+j
                    else:
                        return a-b-c-d-e-f-g-h-i-j
                else:
                    return 0
            else:
                return 1
        else:
            return 2
    else:
        return 3
''',
                commit_hash="test3",
                branch="main",
                symbol="x"
            )
        ]
        
        print("Evaluating code variants:")
        
        for i, variant in enumerate(test_variants, 1):
            print(f"\nVariant {i}: {variant.symbol}")
            
            # Evaluate quality
            evaluated_variant = quality_evaluator.evaluate_variant(variant)
            
            # Display metrics
            metrics = evaluated_variant.metrics
            print(f"  Performance: {metrics.get('performance', 0):.3f}")
            print(f"  Readability: {metrics.get('readability', 0):.3f}")
            print(f"  Complexity:  {metrics.get('complexity', 0):.3f}")
            print(f"  Redundancy:  {metrics.get('redundancy', 0):.3f}")
            print(f"  Style score: {metrics.get('style_score', 0):.3f}")
            print(f"  Errors:      {metrics.get('error_count', 0)}")
            print(f"  Warnings:    {metrics.get('warning_count', 0)}")
            
            # Show objective values
            if evaluated_variant.objective_values:
                print(f"  Objectives:  {[f'{v:.3f}' for v in evaluated_variant.objective_values]}")
            
            # Show available tools
            eval_results = evaluated_variant.execution_results.get('quality_evaluation', {})
            if eval_results.get('evaluation_success'):
                tools = eval_results.get('tools_used', [])
                print(f"  Tools used:  {', '.join(tools) if tools else 'basic analysis'}")
        
        print("\nQuality evaluation: ✓ PASSED")
        
    except Exception as e:
        print(f"Quality evaluation error: {e}")
    
    print()


def demo_integrated_analysis():
    """Demonstrate integrated analysis workflow"""
    print("=== Integrated Analysis Demo ===")
    
    temp_dir = tempfile.mkdtemp()
    repo_path = Path(temp_dir) / "integrated_demo"
    
    try:
        # Setup repository
        repo_manager = RepositoryManager(str(repo_path))
        repo_manager.init_repo(str(repo_path))
        
        create_sample_code_files(repo_path)
        repo_manager.repo.index.add(["main.py", "utils.py"])
        repo_manager.repo.index.commit("Initial commit")
        
        # Initialize all analyzers
        temporal_analyzer = TemporalAnalyzer(str(repo_path))
        spatial_analyzer = SpatialAnalyzer(str(repo_path))
        quality_evaluator = QualityEvaluator()
        
        # Integrated workflow
        print("1. Spatial analysis: Extract related code")
        variant = spatial_analyzer.extract_related_code("main.py", "fibonacci")
        
        print("2. Quality evaluation: Assess code quality")
        variant = quality_evaluator.evaluate_variant(variant)
        
        print("3. Temporal analysis: Build time index")
        time_index = temporal_analyzer.build_time_index("main.py", "fibonacci")
        
        # Combine results
        print("\nIntegrated Analysis Results:")
        print(f"Symbol: {variant.symbol}")
        print(f"Quality metrics:")
        for metric, value in variant.metrics.items():
            if isinstance(value, (int, float)):
                print(f"  {metric}: {value:.3f}")
        
        print(f"Temporal metrics:")
        print(f"  Total commits: {time_index['total_commits']}")
        print(f"  Change frequency: {time_index['change_frequency']['commits_per_day']:.3f}")
        
        print(f"Spatial metrics:")
        spatial_results = variant.execution_results
        print(f"  Related symbols: {len(spatial_results.get('related_symbols', {}))}")
        print(f"  Dependencies: {len(spatial_results.get('dependencies', []))}")
        
        # Cleanup
        spatial_analyzer.cleanup()
        
        print("Integrated analysis: ✓ PASSED")
        
    except Exception as e:
        print(f"Integrated analysis error: {e}")
    finally:
        shutil.rmtree(temp_dir, ignore_errors=True)
    
    print()


def main():
    """Run all analysis layer demos"""
    print("🔍 Analysis Layer Demo")
    print("=" * 50)
    print()
    
    try:
        demo_temporal_analyzer()
        demo_spatial_analyzer()
        demo_quality_evaluator()
        demo_integrated_analysis()
        
        print("🎉 All analysis demos completed successfully!")
        print()
        print("Analysis layer components are working correctly:")
        print("✓ TemporalAnalyzer - Git diff analysis and time indexing")
        print("✓ SpatialAnalyzer - Code relationship extraction")
        print("✓ QualityEvaluator - Multi-dimensional quality assessment")
        print("✓ Integrated workflow - Combined analysis pipeline")
        
    except Exception as e:
        print(f"❌ Demo failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
