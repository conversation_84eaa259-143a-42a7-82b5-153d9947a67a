#!/usr/bin/env python3
"""
Demo script for the data layer functionality.

This script demonstrates the usage of the data layer components
including CodeVariant, RepositoryManager, DatabaseManager, and LogStorage.
"""

import sys
import tempfile
import shutil
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from data.models import CodeVariant, EvolutionConfig
from data.repository import RepositoryManager, RepositoryError
from data.database import DatabaseManager, DatabaseError
from data.storage import LogStorage, LogLevel


def demo_code_variant():
    """Demonstrate CodeVariant functionality"""
    print("=== CodeVariant Demo ===")
    
    # Create a basic variant
    variant = CodeVariant(
        code="def fibonacci(n):\n    if n <= 1:\n        return n\n    return fibonacci(n-1) + fibonacci(n-2)",
        commit_hash="abc123def456",
        branch="main",
        file_path="fibonacci.py",
        symbol="fibonacci"
    )
    
    print(f"Created variant for symbol: {variant.symbol}")
    print(f"Branch: {variant.branch}")
    print(f"Timestamp: {variant.timestamp}")
    
    # Update metrics
    variant.update_metrics({
        "performance": 0.7,
        "readability": 0.9,
        "complexity": 0.6,
        "redundancy": 0.2
    })
    
    # Set Pareto information
    variant.set_pareto_info(rank=1, objectives=[0.7, 0.9, 0.6, 0.2])
    
    # Set grid position
    variant.set_grid_position((2, 3))
    
    print(f"Metrics: {variant.metrics}")
    print(f"Pareto rank: {variant.pareto_rank}")
    print(f"Grid position: {variant.grid_pos}")
    
    # Test serialization
    data = variant.to_dict()
    restored_variant = CodeVariant.from_dict(data)
    print(f"Serialization test: {'✓ PASSED' if restored_variant.code == variant.code else '✗ FAILED'}")
    
    print()


def demo_repository_manager():
    """Demonstrate RepositoryManager functionality"""
    print("=== RepositoryManager Demo ===")
    
    # Create temporary directory
    temp_dir = tempfile.mkdtemp()
    repo_path = Path(temp_dir) / "demo_repo"
    
    try:
        # Initialize repository
        repo_manager = RepositoryManager(str(repo_path))
        repo_manager.init_repo(str(repo_path))
        print(f"Initialized repository at: {repo_path}")
        
        # Create a variant and commit it
        variant = CodeVariant(
            code="def hello_world():\n    return 'Hello, Evolution!'",
            commit_hash="",
            branch="main",
            file_path="hello.py",
            symbol="hello_world"
        )
        
        commit_hash = repo_manager.commit_variant(variant)
        print(f"Committed variant with hash: {commit_hash}")
        
        # Create a new branch
        repo_manager.create_branch("feature-branch")
        print("Created feature branch")
        
        # List branches
        branches = repo_manager.list_branches()
        print(f"Available branches: {branches}")
        
        # Get commit info
        commit_info = repo_manager.get_commit_info(commit_hash)
        print(f"Commit info: {commit_info['message']}")
        
        print("Repository operations: ✓ PASSED")
        
    except RepositoryError as e:
        print(f"Repository error: {e}")
    finally:
        # Cleanup
        shutil.rmtree(temp_dir, ignore_errors=True)
    
    print()


def demo_database_manager():
    """Demonstrate DatabaseManager functionality"""
    print("=== DatabaseManager Demo ===")
    
    # Create temporary database
    temp_dir = tempfile.mkdtemp()
    db_path = Path(temp_dir) / "demo.db"
    
    try:
        # Initialize database
        db_manager = DatabaseManager(str(db_path))
        print(f"Initialized database at: {db_path}")
        
        # Create and store variants
        variants = []
        for i in range(3):
            variant = CodeVariant(
                code=f"def function_{i}():\n    return {i}",
                commit_hash=f"hash_{i:03d}",
                branch="main" if i < 2 else "feature",
                file_path=f"file_{i}.py",
                symbol=f"function_{i}",
                metrics={"performance": 0.8 + i * 0.1},
                pareto_rank=i + 1,
                objective_values=[0.8 + i * 0.1, 0.9 - i * 0.1]
            )
            variants.append(variant)
            
            variant_id = db_manager.store_variant(variant)
            print(f"Stored variant {i} with ID: {variant_id}")
            
            # Store in Pareto archive
            db_manager.store_pareto_archive(
                variant_id, 
                generation=1, 
                rank=variant.pareto_rank, 
                objectives=variant.objective_values
            )
        
        # Query variants
        main_variants = db_manager.query_variants(branch="main")
        print(f"Found {len(main_variants)} variants in main branch")
        
        # Query Pareto archive
        pareto_archive = db_manager.query_pareto_archive(generation=1)
        print(f"Pareto archive has {len(pareto_archive)} entries")
        
        # Log evolution events
        db_manager.log_evolution_event(
            "optimization_start",
            branch="main",
            generation=1,
            details={"population_size": 50, "objectives": ["performance", "readability"]}
        )
        
        # Get statistics
        stats = db_manager.get_statistics()
        print(f"Database statistics: {stats}")
        
        print("Database operations: ✓ PASSED")
        
    except DatabaseError as e:
        print(f"Database error: {e}")
    finally:
        # Cleanup
        shutil.rmtree(temp_dir, ignore_errors=True)
    
    print()


def demo_log_storage():
    """Demonstrate LogStorage functionality"""
    print("=== LogStorage Demo ===")
    
    # Create temporary log directory
    temp_dir = tempfile.mkdtemp()
    log_dir = Path(temp_dir) / "logs"
    
    try:
        # Initialize log storage
        log_storage = LogStorage(str(log_dir))
        print(f"Initialized log storage at: {log_dir}")
        
        # Log different types of events
        log_storage.log_evolution_event(
            "mutation_applied",
            {"variant_id": 123, "mutation_type": "function_rename"},
            LogLevel.INFO
        )
        
        log_storage.log_performance_metrics(
            "optimization_cycle",
            {"duration": 2.5, "memory_usage": 150, "variants_processed": 50}
        )
        
        log_storage.log_error(
            "compilation_error",
            "Syntax error in generated code",
            {"line": 42, "file": "generated.py"}
        )
        
        log_storage.log_git_operation(
            "commit",
            {"hash": "abc123", "files": ["test.py"]},
            success=True
        )
        
        log_storage.log_database_operation(
            "insert_variant",
            {"table": "variants", "id": 456},
            success=True
        )
        
        print("Logged various events")
        
        # Get log summary
        summary = log_storage.get_log_summary()
        print(f"Log summary: {len(summary['log_files'])} log files, total size: {summary['total_size']} bytes")
        
        # Retrieve some logs
        evolution_logs = log_storage.get_logs("evolution", limit=5)
        print(f"Retrieved {len(evolution_logs)} evolution log entries")
        
        print("Log storage operations: ✓ PASSED")
        
    except Exception as e:
        print(f"Log storage error: {e}")
    finally:
        # Cleanup
        shutil.rmtree(temp_dir, ignore_errors=True)
    
    print()


def demo_evolution_config():
    """Demonstrate EvolutionConfig functionality"""
    print("=== EvolutionConfig Demo ===")
    
    config = EvolutionConfig(
        repo_path="/path/to/repo",
        symbol="target_function",
        island_count=4,
        grid_size=(10, 10),
        objectives=["performance", "readability", "complexity", "redundancy"],
        max_generations=100,
        population_size=50
    )
    
    print(f"Evolution config created:")
    print(f"  Repository: {config.repo_path}")
    print(f"  Target symbol: {config.symbol}")
    print(f"  Islands: {config.island_count}")
    print(f"  Grid size: {config.grid_size}")
    print(f"  Objectives: {config.objectives}")
    print(f"  Max generations: {config.max_generations}")
    print(f"  Population size: {config.population_size}")
    
    print("Evolution config: ✓ PASSED")
    print()


def main():
    """Run all demos"""
    print("🚀 Data Layer Demo")
    print("=" * 50)
    print()
    
    try:
        demo_code_variant()
        demo_repository_manager()
        demo_database_manager()
        demo_log_storage()
        demo_evolution_config()
        
        print("🎉 All demos completed successfully!")
        print()
        print("Data layer components are working correctly:")
        print("✓ CodeVariant - Unified data model")
        print("✓ RepositoryManager - Git operations")
        print("✓ DatabaseManager - SQLite storage")
        print("✓ LogStorage - Structured logging")
        print("✓ EvolutionConfig - Configuration management")
        
    except Exception as e:
        print(f"❌ Demo failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
