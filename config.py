"""
Configuration settings for the evolution programming tool.
"""

from pathlib import Path
from typing import Dict, List, Tuple, Any

# Project paths
PROJECT_ROOT = Path(__file__).parent
SRC_DIR = PROJECT_ROOT / "src"
DATA_DIR = PROJECT_ROOT / "data"
LOGS_DIR = PROJECT_ROOT / "logs"
TESTS_DIR = PROJECT_ROOT / "tests"

# Default database path
DEFAULT_DB_PATH = DATA_DIR / "evolution.db"

# Default log directory
DEFAULT_LOG_DIR = LOGS_DIR

# Evolution configuration defaults
DEFAULT_EVOLUTION_CONFIG = {
    "island_count": 4,
    "grid_size": (10, 10),
    "objectives": ["performance", "readability", "complexity", "redundancy"],
    "max_generations": 100,
    "population_size": 50,
    "mutation_rate": 0.1,
    "crossover_rate": 0.8,
    "elite_ratio": 0.1
}

# Quality metrics configuration
QUALITY_METRICS = {
    "performance": {
        "weight": 0.3,
        "higher_is_better": True,
        "description": "Execution performance (speed)"
    },
    "readability": {
        "weight": 0.25,
        "higher_is_better": True,
        "description": "Code readability score"
    },
    "complexity": {
        "weight": 0.25,
        "higher_is_better": False,
        "description": "Cyclomatic complexity"
    },
    "redundancy": {
        "weight": 0.2,
        "higher_is_better": False,
        "description": "Code redundancy ratio"
    }
}

# Git configuration
GIT_CONFIG = {
    "default_branch": "main",
    "island_prefix": "island_",
    "tag_prefix": "pareto_",
    "commit_message_template": "Evolution variant for {symbol} (Pareto rank: {rank})"
}

# Database configuration
DATABASE_CONFIG = {
    "connection_timeout": 30,
    "max_connections": 10,
    "backup_interval": 3600,  # seconds
    "cleanup_interval": 86400  # seconds
}

# Logging configuration
LOGGING_CONFIG = {
    "max_file_size": 10 * 1024 * 1024,  # 10MB
    "backup_count": 5,
    "log_levels": {
        "evolution": "INFO",
        "performance": "INFO",
        "error": "ERROR",
        "git": "INFO",
        "database": "INFO"
    },
    "cleanup_days": 30
}

# Performance monitoring
PERFORMANCE_CONFIG = {
    "enable_profiling": False,
    "memory_limit": 1024 * 1024 * 1024,  # 1GB
    "timeout_seconds": 300,  # 5 minutes
    "benchmark_iterations": 10
}


def ensure_directories():
    """Ensure all required directories exist"""
    directories = [DATA_DIR, LOGS_DIR]
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)


def get_evolution_config(**overrides) -> Dict[str, Any]:
    """Get evolution configuration with optional overrides"""
    config = DEFAULT_EVOLUTION_CONFIG.copy()
    config.update(overrides)
    return config


def get_database_path(custom_path: str = None) -> str:
    """Get database path"""
    if custom_path:
        return custom_path
    ensure_directories()
    return str(DEFAULT_DB_PATH)


def get_log_directory(custom_dir: str = None) -> str:
    """Get log directory path"""
    if custom_dir:
        return custom_dir
    ensure_directories()
    return str(DEFAULT_LOG_DIR)


# Initialize directories on import
ensure_directories()
